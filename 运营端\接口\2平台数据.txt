1、获取平台数据
/sw/admin/report/getPlatformReport
post
content-type:application/json
参数:
{
	pageNo:
	pageSize:

}
响应:
{
	code: 业务响应码 200 500...  
	message: 这个是异常的时候的消息提示
	data:{
		pageNo: '第几页'
		pageSize:'一页多少页'
		totalPages:'总页数'
		totalElements:'总数据量'
		data:[
		{
  "reportDate": "统计日期（格式：yyyy-MM-dd）",
  "dailyActiveUsers": "日活跃用户数（DAU）",
  "landlordCount": "平台注册房东总数",
  "roomCount": "平台房源总数",
  "rentedCount": "当前已租房源数量",
  "availableCount": "当前待租房源数量",
  "merchantCount": "平台入驻商户总数",
  "agencyFee": "平台代收费用总额（单位：元）",
  "merchantPayment": "商户支付给平台的费用总额（单位：元）",
  "ecommerceOrders": "电商平台下单数量",
  "orderAmount": "电商平台下单总金额（单位：元）",
  "rebateAmount": "平台返佣总金额（单位：元）"
}]
	}
}

2、房东运营数据
/sw/admin/report/getLandlordReport
post
content-type:application/json
参数:
{
	pageNo:
	pageSize:

}
响应:
{
	code: 业务响应码 200 500...  
	message: 这个是异常的时候的消息提示
	data:{
		pageNo: '第几页'
		pageSize:'一页多少页'
		totalPages:'总页数'
		totalElements:'总数据量'
		data:[
		{
  "reportDate": "统计日期",
  "landlordCount": "房东数量",
  "roomCount": "房间总数",
  "rentedRoomCount": "已租房间数量",
  "availableRoomCount": "待租房间数量",
  "collectionAmount": "收款金额（总收款）",
  "electricityFeeAmount": "电费收入",
  "rentAmount": "房租收入",
  "withdrawalAmount": "提现金额",
  "totalIncome": "总收入（各种收入总和）",
  "totalWithdrawal": "总提现金额",
  "balance": "平台余额（总收入 - 总提现）"
}]
	}
}
3、商户运营数据
/sw/admin/report/getMerchantReport
post
content-type:application/json
参数:
{
	pageNo:
	pageSize:

}
响应:
{
	code: 业务响应码 200 500...  
	message: 这个是异常的时候的消息提示
	data:{
		pageNo: '第几页'
		pageSize:'一页多少页'
		totalPages:'总页数'
		totalElements:'总数据量'
		data:[
		{
  "reportDate": "统计日期",
  "merchantCount": "商户总数",
  "newMerchantCount": "新增商户数",
  "paidMerchantCount": "付费商户数",
  "paymentAmount": "付费金额",
  "paymentOrderCount": "付费订单数（如果需要可以添加）"
}]
	}
}
4、电商运营数据
/sw/admin/report/getEcommerceReport
post
content-type:application/json
参数:
{
	pageNo:
	pageSize:

}
响应:
{
	code: 业务响应码 200 500...  
	message: 这个是异常的时候的消息提示
	data:{
		pageNo: '第几页'
		pageSize:'一页多少页'
		totalPages:'总页数'
		totalElements:'总数据量'
		data:[
		{
  "reportDate": "统计日期",
  "dailyActiveUsers": "日活跃用户数(DAU)",
  "orderCount": "订单总数",
  "orderAmount": "订单总金额",
  "commissionAmount": "佣金金额",
  "conversionRate": "转化率(订单数/日活，可选)",
  "averageOrderValue": "平均订单金额(可选)"
}]
	}
}
