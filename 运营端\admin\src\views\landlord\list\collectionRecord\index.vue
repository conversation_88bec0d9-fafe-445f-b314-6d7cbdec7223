<template>
  <div class="">
    <el-table
      v-loading="listLoading"
      :data="list"
      max-height="580"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
      <el-table-column label="支付人" align="center" prop="payName" />
      <el-table-column label="支付类型" align="center" prop="type">
          <template #default="{ row }">
            <!-- <el-tag :type="typeFilter(row.type).type">{{
              typeFilter(row.type).name
            }}</el-tag> -->
            <span>{{ typeFilter(row.type).name }}</span>
          </template>
        </el-table-column>
      <el-table-column label="支付金额" align="center" prop="amount" />
      <el-table-column label="支付时间" align="center" prop="payTime">
        <template #default="{ row }">{{
          parseTime(row.payTime, "YYYY-MM-DD HH:mm:ss")
        }}</template>
      </el-table-column>
      <!-- <el-table-column label="所属物业" align="center" prop="openKey" /> -->
    </el-table>

    <div class="pagination">
      <el-pagination
        @size-change="fetchData"
        @current-change="fetchData"
        :current-page.sync="querySearch.pageNo"
        :page-sizes="[15, 30, 50, 100]"
        :page-size.sync="querySearch.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="querySearch.total"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import * as api from "@/api/landlord";
export default {
  name: "LandlordwithdrawalRecord",
  data() {
    return {
      list: null,
      listLoading: true,
      querySearch: {
        pageNo: 1,
        pageSize: 50,
        total: 0,
      },

      typeOptions: [
        {
          name: "全部",
          value: "-1",
          type: "success",
        },
        {
          name: "房租",
          value: "0",
          type: "info",
        },
        {
          name: "电费",
          value: "1",
          type: "success",
        },
        {
          name: "水费",
          value: "2",
          type: "success",
        },
      ],
    };
  },
  computed: {
    openKey() {
      return this.$route.params.openKey || "";
    },
  },
  mounted() {
    this.fetchData();
  },
  activated() {
    this.fetchData();
  },
  methods: {
    typeFilter(type) {
      return (
        this.typeOptions.find((e) => e.value == type) || {
          name: "未知",
          value: "",
          type: "",
        }
      );
    },

    async fetchData() {
      try {
        this.listLoading = true;
        const { data: res } = await api.getPayHistoryList({
          ...this.querySearch,
          openKey: this.openKey,
        });
        const { data, pageNo, pageSize, totalElements } = res;
        this.list = data;
        this.querySearch = {
          pageNo,
          pageSize,
          total: totalElements,
          status: this.querySearch.status,
        };
      } catch (err) {
        console.error(err);
      } finally {
        this.listLoading = false;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.pagination {
  padding-top: 20px;
  text-align: right;
}
</style>