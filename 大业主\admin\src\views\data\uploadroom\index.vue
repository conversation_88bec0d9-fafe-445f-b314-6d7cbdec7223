<!--
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-05-20 15:59:55
 * @LastEditors: wangxingyue
 * @LastEditTime: 2025-06-16 16:21:28
 * @FilePath: \ban-ban\大业主\admin\src\views\data\uploadroom\index.vue
-->
<template>
  <div class="app-container">
    <div class="card-content">
      <div class="description">
        <i class="el-icon-info info-icon"></i>
        <span
          >通过Excel文件批量导入房间信息，请先下载模版，按照模版格式填写后再上传。</span
        >
      </div>

      <div class="steps">
        <div class="step-item">
          <div class="step-number">1</div>
          <div class="step-text">下载Excel模版</div>
        </div>
        <div class="step-arrow"><i class="el-icon-arrow-right"></i></div>
        <div class="step-item">
          <div class="step-number">2</div>
          <div class="step-text">填写房间信息</div>
        </div>
        <div class="step-arrow"><i class="el-icon-arrow-right"></i></div>
        <div class="step-item">
          <div class="step-number">3</div>
          <div class="step-text">上传Excel文件</div>
        </div>
      </div>

      <div class="import-container">
        <el-button
          size="medium"
          type="primary"
          icon="el-icon-download"
          @click="downloadTemplate()"
        >
          下载模版
        </el-button>

        <el-upload
          class="upload-button"
          :action="`${BASE_URL}/sw/wy/project/room/importProjectRoom`"
          :on-success="handleSuccess"
          :limit="1"
          :before-upload="handleUpload"
          accept=".xlsx"
          with-credentials
          :multiple="false"
          :show-file-list="false"
          name="file"
          :headers="{
            token,
          }"
        >
          <el-button size="medium" type="success" icon="el-icon-upload">
            导入房间
          </el-button>
        </el-upload>
      </div>

      <div class="tips">
        <p><i class="el-icon-warning-outline"></i> 注意事项：</p>
        <ul>
          <li>仅支持 .xlsx 格式的Excel文件</li>
          <li>请确保填写的数据符合模版要求，否则可能导入失败</li>
          <li>导入过程中请勿刷新页面</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";

export default {
  name: "ImportRoom",
  data() {
    return {
      BASE_URL: process.env.VUE_APP_SERVER_URL,
    };
  },
  computed: {
    token() {
      return getToken();
    },
  },
  methods: {
    downloadTemplate() {
      window.open("/xlsx/房间信息模板.xlsx", "_blank");
    },
    handleSuccess(response, file, fileList) {
      console.log("handleSuccess==>", response, file, fileList);
      const { code, data, message } = response;
      if (code == 200) {
        this.$message({
          type: "success",
          message: "房间信息导入成功！",
          duration: 3000,
        });
      } else {
        this.$message({
          type: "error",
          message: message || "上传失败",
          duration: 5000,
        });
      }
    },
    handleUpload(file) {
      console.log("handleUpload==>", file);
      const is_xlsx = file.name.indexOf(".xlsx") !== -1;
      if (!is_xlsx) {
        this.$message({
          type: "warning",
          message: "上传文件只能是 xlsx 格式!",
          duration: 3000,
        });
        return false;
      }
      return true;
    },
  },
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.card-content {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 30px;
}

.description {
  display: flex;
  align-items: center;
  background-color: #f0f9eb;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 25px;
  color: #67c23a;
}

.info-icon {
  font-size: 18px;
  margin-right: 10px;
}

.steps {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 30px;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.step-number {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #409eff;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 8px;
  font-weight: bold;
  transition: all 0.3s ease;
}

.step-item:hover .step-number {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.step-text {
  font-size: 14px;
  color: #606266;
}

.step-arrow {
  margin: 0 20px;
  color: #dcdfe6;
  font-size: 20px;
}

.import-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin: 30px 0;
}

.upload-button {
  display: inline-block;
}

.import-container .el-button {
  padding: 12px 24px;
  transition: all 0.3s ease;
}

.import-container .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tips {
  background-color: #fef0f0;
  border-radius: 4px;
  padding: 15px;
  color: #f56c6c;
  font-size: 14px;
  margin-top: 20px;
}

.tips p {
  margin-top: 0;
  margin-bottom: 10px;
  font-weight: bold;
  display: flex;
  align-items: center;
}

.tips p i {
  margin-right: 5px;
}

.tips ul {
  margin: 0;
  padding-left: 20px;
}

.tips li {
  line-height: 1.8;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .app-container {
    padding: 10px;
  }

  .card-content {
    padding: 20px;
  }

  .steps {
    flex-wrap: wrap;
  }

  .step-item {
    margin-bottom: 15px;
  }

  .step-arrow {
    margin: 0 10px;
  }

  .import-container {
    flex-direction: column;
    gap: 15px;
  }

  .import-container .el-button {
    width: 100%;
    max-width: 300px;
  }
}
</style>
