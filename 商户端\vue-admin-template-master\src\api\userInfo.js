/*
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-04-03 11:26:44
 * @LastEditors: wangxingyue
 * @LastEditTime: 2025-05-26 16:11:33
 * @FilePath: \ban-ban\商户端\vue-admin-template-master\src\api\userInfo.js
 */
import request from "@/utils/request";


//获取商户资料
export function getShopInfo() {
  return request({
    url: "/shop/getShopInfo",
    method: "get",
  });
}

//设置商户资料
export function setShopInfo(data) {
  return request({
    url: "/shop/setShopInfo",
    method: "POST",
    data,
    requestType: "json",
  });
}

//获取服务列表
export function typegetServiceTypeList() {
  return request({
    url: "/service/typegetServiceTypeList",
    method: "get",
  });
}

// //1、获取大分类类型
// export function getAllBaseType() {
//   return request({
//     url: "/service/type/getAllBaseType",
//     method: "get",
//   });
// }

// //获取小分类类型
// export function getServiceTypeByBaseType(baseTypeId) {
//   return request({
//     url: "/service/type/getServiceTypeByBaseType",
//     method: "get",
//     params: {
//       baseTypeId,
//     },
//   });
// }

// //获取省接口
// export function getProvince() {
//   return request({
//     url: "/area/getProvince",
//     method: "get",
//   });
// }

// //获取市接口
// export function getCity(parentCode) {
//   return request({
//     url: "/area/getCity",
//     method: "get",
//     params: { parentCode },
//   });
// }
// //获取区接口
// export function getArea(parentCode) {
//   return request({
//     url: "/area/getArea",
//     method: "get",
//     params: { parentCode },
//   });
// }
