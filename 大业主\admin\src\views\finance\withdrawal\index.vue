<template>
  <div class="app-container">
    <!-- 账户概况展示 -->
    <div class="report-data">
      <div class="overview-cards">
        <div class="overview-card">
          <div class="card-label">当前余额</div>
          <div class="card-value">{{ wyAmount.remainAmount || "0" }}元</div>
        </div>
        <div class="overview-card">
          <div class="card-label">总收入</div>
          <div class="card-value">{{ wyAmount.totalAmount || "0" }}元</div>
        </div>
        <div class="overview-card">
          <div class="card-label">已提现金额</div>
          <div class="card-value">{{ wyAmount.withdrawAmount || "0" }}元</div>
        </div>
      </div>
    </div>

    <!-- 提现按钮 -->
    <div class="withdrawal-action">
      <el-button type="primary" @click="showWithdrawalDialog"
        >申请提现</el-button
      >
      <el-button type="info" @click="showRecordDialog"
        >提现记录</el-button
      >
    </div>

    <div class="table-container">
      <el-table
        v-loading="listLoading"
        :data="list"
        max-height="580"
        style="width: 100%"
        element-loading-text="Loading"
        border
        fit
        highlight-current-row
      >
        <!-- <el-table-column label="物业标识符" align="center" prop="openKey" /> -->
        <el-table-column label="时间" align="center" prop="payTime">
          <template #default="{ row }">
            {{ parseTime(row.payTime, "YYYY-MM-DD HH:mm:ss") }}
          </template>
        </el-table-column>
        <el-table-column label="收入增减" align="center" prop="amount" />
        <el-table-column label="收入来源" align="center">
          <template #default="{ row }">
            <template v-if="row.type == 3">
              {{ getTypeText(row.type) }}
            </template>
            <template v-else>
              {{ row.projectName }} - {{ row.roomNo }} - {{ getTypeText(row.type) }}
            </template>
          </template>
        </el-table-column>
        <el-table-column label="余额" align="center" prop="surplus" />
      </el-table>
      <div class="pagination">
        <el-pagination
          @size-change="fetchData"
          @current-change="fetchData"
          :current-page.sync="querySearch.pageNo"
          :page-sizes="[15, 30, 50, 100]"
          :page-size.sync="querySearch.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="querySearch.total"
        >
        </el-pagination>
      </div>
    </div>

    <!-- 提现申请对话框 -->
    <el-dialog
      title="申请提现"
      :visible.sync="withdrawalDialogVisible"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form
        :model="withdrawalForm"
        :rules="withdrawalRules"
        ref="withdrawalForm"
        label-width="100px"
      >
        <el-form-item label="可提现金额">
          <span>{{ wyAmount.remainAmount || "0" }}元</span>
        </el-form-item>
        <el-form-item label="提现金额" prop="amount">
          <el-input
            v-model.number="withdrawalForm.amount"
            placeholder="请输入提现金额"
            type="number"
            :min="1"
            :max="Number(wyAmount.remainAmount || 0)"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="withdrawalDialogVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="submitWithdrawal"
          :loading="submitting"
          >确 定</el-button
        >
      </div>
    </el-dialog>

    <!-- 提现记录对话框 -->
    <el-dialog
      title="提现记录"
      :visible.sync="recordDialogVisible"
      width="50%"
      :close-on-click-modal="false"
    >
      <el-table
        v-loading="recordLoading"
        :data="recordList"
        max-height="500"
        element-loading-text="Loading"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="申请时间" align="center" prop="applyTime">
          <template #default="{ row }">
            {{ parseTime(row.applyTime, "YYYY-MM-DD") }}
          </template>
        </el-table-column>
        <el-table-column label="结束时间" align="center" prop="endTime">
          <template #default="{ row }">
            {{ parseTime(row.endTime, "YYYY-MM-DD") }}
          </template>
        </el-table-column>
        <el-table-column label="提现金额" align="center" prop="amount" />
        <el-table-column label="状态" align="center" prop="status">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">{{
              getStatusText(row.status)
            }}</el-tag>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          @size-change="fetchRecordData"
          @current-change="fetchRecordData"
          :current-page.sync="recordQuery.pageNo"
          :page-sizes="[15, 30, 50, 100]"
          :page-size.sync="recordQuery.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="recordQuery.total"
        >
        </el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as api from "@/api/finance.js";
export default {
  data() {
    // 验证提现金额
    const validateAmount = (rule, value, callback) => {
      if (!value) {
        return callback(new Error("请输入提现金额"));
      }
      if (value <= 0) {
        return callback(new Error("提现金额必须大于0"));
      }
      const remainAmount = Number(this.wyAmount.remainAmount || 0);
      if (value > remainAmount) {
        return callback(
          new Error(`提现金额不能超过可提现金额${remainAmount}元`)
        );
      }
      callback();
    };

    return {
      wyAmount: {
        totalAmount: "0",
        eleAmount: "0",
        rentAmount: "0",
        remainAmount: "0",
        withdrawAmount: "0",
      },
      // 收益明细相关
      list: [],
      listLoading: false,
      querySearch: {
        pageNo: 1,
        pageSize: 50,
        total: 0,
      },
      // 提现记录相关
      recordDialogVisible: false,
      recordLoading: false,
      recordList: [],
      recordQuery: {
        pageNo: 1,
        pageSize: 50,
        total: 0
      },
      // 提现相关
      withdrawalDialogVisible: false,
      submitting: false,
      withdrawalForm: {
        amount: "",
      },
      withdrawalRules: {
        amount: [
          { required: true, message: "请输入提现金额", trigger: "blur" },
          { validator: validateAmount, trigger: "blur" },
        ],
      },
      
    };
  },
  created() {
    this.fetchWyAmount();
    this.fetchData();
  },
  methods: {
    async fetchWyAmount() {
      try {
        const { data } = await api.getWyAmount();

        this.wyAmount = data;
      } catch (err) {
        console.error(err);
        this.$message.error("获取账户概况失败");
      }
    },
    async fetchData() {
      try {
        this.listLoading = true;
        const { data: res } = await api.getWyPayHistoryDetail({
          ...this.querySearch,
        });
        console.log(res);
        const { data, pageNo, pageSize, totalElements } = res;
        this.list = data;
        this.querySearch = {
          pageNo,
          pageSize,
          total: totalElements,
        };
      } catch (err) {
        console.error(err);
      } finally {
        this.listLoading = false;
      }
    },
    getStatusType(status) {
      const statusMap = {
        0: "warning", // 申请中
        1: "danger", // 已拒绝
        2: "success", // 已成功
      };
      return statusMap[status] || "info";
    },
    getStatusText(status) {
      const statusMap = {
        0: "申请中",
        1: "已拒绝",
        2: "已成功",
      };
      return statusMap[status] || "未知状态";
    },
    // 获取类型文本
    getTypeText(type) {
      const typeMap = {
        0: "房租",
        1: "电费",
        2: "水费",
        3: "提现",
        4: "其他收入"
      };
      return typeMap[type] || "未知类型";
    },
    // 显示提现对话框
    showWithdrawalDialog() {
      // this.withdrawalForm.amount = "";
      this.withdrawalForm.amount = this.wyAmount.remainAmount;
      this.withdrawalDialogVisible = true;
    },
    // 显示提现记录对话框
    showRecordDialog() {
      this.recordDialogVisible = true;
      this.fetchRecordData();
    },
    // 获取提现记录数据
    async fetchRecordData() {
      try {
        this.recordLoading = true;
        const { data: res } = await api.getWithdrawalApplyList({
          ...this.recordQuery,
        });
        const { data, pageNo, pageSize, totalElements } = res;
        this.recordList = data;
        this.recordQuery = {
          pageNo,
          pageSize,
          total: totalElements
        };
      } catch (err) {
        console.error(err);
        this.$message.error("获取提现记录失败");
      } finally {
        this.recordLoading = false;
      }
    },
    // 提交提现申请
    submitWithdrawal() {
      this.$refs.withdrawalForm.validate(async (valid) => {
        if (valid) {
          try {
            this.submitting = true;
            await api.submitWithdrawalApply({
              amount: this.withdrawalForm.amount,
            });
            this.$message.success("提现申请提交成功");
            this.withdrawalDialogVisible = false;
            // 刷新提现列表和资金情况
            this.fetchWyAmount();
            this.fetchData();
          } catch (err) {
            console.error(err);
            this.$message.error("提现申请提交失败");
          } finally {
            this.submitting = false;
          }
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.withdrawal-action {
  margin: 15px 0;
}
.report-data {
  margin-bottom: 30px;
  .data-item {
    text-align: center;
    .label {
      font-size: 14px;
      color: #909399;
      margin-bottom: 8px;
    }
    .value {
      font-size: 20px;
      color: #303133;
      font-weight: bold;
    }
  }
  .smaller-text {
    font-size: 14px;
  }
  .larger-text {
    font-size: 20px;
    // font-weight: bold;
  }
  .overview-cards {
    display: flex;
    justify-content: space-between;
    gap: 20px;
    
    .overview-card {
      flex: 1;
      background-color: #f5f7fa;
      border-radius: 4px;
      padding: 15px;
      text-align: center;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
      
      .card-label {
        font-size: 14px;
        color: #606266;
        margin-bottom: 10px;
      }
      
      .card-value {
        font-size: 18px;
        font-weight: bold;
        color: #303133;
      }
    }
  }
  .simple-overview {
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 4px;
    .overview-item {
      margin-right: 20px;
      font-size: 14px;
      color: #606266;
      .value {
        font-size: 16px;
        font-weight: bold;
        color: #303133;
      }
    }
  }
}
.table-container {
  margin-top: 20px;
}
.pagination {
  padding-top: 20px;
  text-align: right;
}
.addBtn {
  padding-bottom: 20px;
  // padding-left: 10px;

  //   display: flex;
  //   align-items: flex-end;
}
.tip {
  color: #555;
  margin-left: 20px;
  .text {
    color: #409eff;
    cursor: pointer;
    &:hover {
      text-decoration: underline;
    }
  }
}
::v-deep .hide .el-upload--picture-card {
  display: none;
}
.qrcode-container {
  display: flex;
  justify-content: center;
  padding: 20px 0;

  img {
    max-width: 200px;
    max-height: 200px;
  }

  .no-qrcode {
    color: #909399;
    font-size: 14px;
  }
}
</style>
