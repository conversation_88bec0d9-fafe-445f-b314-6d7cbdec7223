
1、获取房间的基本电量使用信息

/sw/wy/ele/consump/getRoomInfo
GET
projectId: 项目id
roomNo:房间号

响应:
{
	code:200,
	data:{
		roomId:房间id
		totalUsed:使用的电量
		totalFee:总共的电费

	}
}






2、获取某个房间的用电量
/sw/wy/ele/consump/findByPage

post
参数:
{
  "projectId": "项目id",
  "roomNo":"房屋编号"
  "pageNo": "页码",
  "pageSize": "页大小"
}

响应:
{
	code: 业务响应码 200 500...  
	message: 这个是异常的时候的消息提示
	data:{
		pageNo: '第几页'
		pageSize:'一页多少页'
		totalPages:'总页数'
		totalElements:'总数据量'
		data:[

{
id
  "date": "@Temporal(TemporalType.DATE) 日期",
  "projectId": "项目id",
  "roomId": "房间id",
  "projectName": "项目名",
  "roomNumber": "房间号",
  "collectorNumber": "采集器号",
  "meterAddress": "电表地址",
  "status": "状态",//0 离线  1 在线
  "switchStatus": "拉合闸状态 //0 拉闸  1 合闸",
  "electricityUsage": "用电量(单位：度)",
  "electricityCost": "用电金额",
	"electricityAmount": "电费", ---            电费列放到电费余额前面
  "balance": "电费余额"
  "clearNumber": "清零",
  "openAccountNum": "开户"
		}
			]
	}
}






2、清零
/sw/wy/ele/meter/clearMeter
GET
roomId:

响应:
{
	code:200,
	data:null
}


3、开户
/sw/wy/ele/meter/clearMeter
GET
roomId:

响应:
{
	code:200,
	data:null
}


