/*
 *Author: CBB
 *Date: 2025-03-17 20:32:19
 *LastEditTime: 2025-03-17 20:33:32
 *LastEditors: CBB
 *Description:
 *FilePath: \安小页\商户端\vue-admin-template-master\src\api\pulic.js
 */
import request from "@/utils/request";

export function uploadFile(data) {
  const formData = new FormData();
  for (const key in data) {
    if (Object.prototype.hasOwnProperty.call(data, key)) {
      const element = data[key];
      formData.append(key, element);
    }
  }
  return request({
    baseURL: "",
    url: "/axy/file/uploadFile",
    method: "post",
    requestType: "form-data",
    data: formData,
  });
}
