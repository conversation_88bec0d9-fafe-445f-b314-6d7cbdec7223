<template>
	<div class="wrap">
		<el-tabs v-model="form.type" @tab-click="handleClick">
			<el-tab-pane v-for="(item, index) in tabsOptions" :key="index + item.value" :label="item.label"
				:name="item.value" />
		</el-tabs>

		<div v-loading="loading" class="editBox">
			<VueEditor ref="VueEditor" v-model="form.content" useCustomImageHandler @image-added="handleImageAdded"
				:editor-toolbar="customToolbar" />
		</div>
		<div class="btns">
			<el-button type="primary" @click="submit()" :loading="loading">保存</el-button>
		</div>
	</div>
</template>
<script>
	import {
		VueEditor
	} from 'vue2-editor'
	import Quill from 'quill'
	var fonts = ['Microsoft-YaHei', 'SimSun', 'SimHei', 'KaiTi', 'Arial', 'Times-New-Roman'];
	var Font = Quill.import('formats/font');
	console.log("Font",Font)
	Font.whitelist = fonts; //将字体加入到白名单
	import * as api from "@/api/system";

	// 1 押金说明 关于我们 3 商户上线说明
	export default {
		components: {
			VueEditor,
		},
		data() {
			return {
				tabsOptions: [
                    // {
					// 	label: "关于我们",
					// 	value: "1",
					// 	apiFunction: api.getAbout,
					// },
					// {
					// 	label: "隐私协议",
					// 	value: "2",
					// 	apiFunction: api.getPrivateExplanation,
					// },
					// {
					// 	label: "积分说明",
					// 	value: "3",
					// 	apiFunction: api.getPointsExplanation,
					// },

                    {
						label: "押金说明",
						value: "1",
						apiFunction: api.getSetting,
                        type:0
					},
					{
						label: "关于我们",
						value: "2",
						apiFunction: api.getSetting,
                        type:1
					},
					{
						label: "商户上线说明",
						value: "3",
						apiFunction: api.getSetting,
                        type:3
					}
				],
				loading: false,
				form: {
					type: "1",
					content: "",
					id: "",
				},
				customToolbar: [
					// 字体样式
					['bold', 'italic', 'underline', 'strike'], // 粗体、斜体、下划线、删除线
					['blockquote', 'code-block'], // 引用块、代码块

					// 标题和字体大小
					[{
						'header': 1
					}, {
						'header': 2
					}], // 标题1、标题2
					[{
						'header': [1, 2, 3, 4, 5, 6, false]
					}], // 多级标题下拉菜单
					[{
						'size': ['small', false, 'large', 'huge']
					}], // 字体大小

					// 列表
					[{
						'list': 'ordered'
					}, {
						'list': 'bullet'
					}], // 有序列表、无序列表
					[{
						'script': 'sub'
					}, {
						'script': 'super'
					}], // 下标、上标

					// 缩进和对齐
					[{
						'indent': '-1'
					}, {
						'indent': '+1'
					}], // 减少缩进、增加缩进
					[{
						'direction': 'rtl'
					}], // 文本方向
					[{
						'align': []
					}], // 对齐方式

					// 颜色
					[{
						'color': []
					}, {
						'background': []
					}], // 文字颜色、背景色
					[{
						'font': []
					}, {
						header: [1, 2, 3, 4, 5, 6, false]
					}],

					// 其他功能
					['link', 'image', 'video'], // 链接、图片、视频
					['clean'], // 清除格式

					// 自定义按钮
					['customButton'],

					// 自定义下拉菜单
					[{
						'custom-dropdown': ['选项1', '选项2', '选项3']
					}]
				],

			}
		},
		created() {
			this.handleClick({
				name: "1"
			});
		},
		computed: {
			chooseTabObj() {
				return this.tabsOptions.find((item) => item.value == this.form.type);
			},
		},
		mounted() {

		},
		methods: {

			async handleClick(e) {
				const {
					name
				} = e;
				console.log(name);
				this.loading = true;
				try {
					const tabObj = this.tabsOptions.find(item => item.value === name);
					const {
						data
					} = await tabObj.apiFunction({ type: tabObj.type });
					console.log(data);
					this.form = {
						...data,
						type: name
					};
				} catch (err) {
					console.error(err);
					this.form = {
						type: name,
						content: "",
						id: "",
					};
				} finally {
					this.loading = false;
				}
			},
			async handleImageAdded(file, Editor, cursorLocation, resetUploader) {
				const formData = new FormData();
				formData.append("file", file);
				const {
					data
				} = await api.uploadFile(formData);
				console.log(data);
				Editor.insertEmbed(cursorLocation, "image", data);
				resetUploader();
			},
			async submit() {
				this.loading = true;
				try {
					const tabObj = this.tabsOptions.find(item => item.value === this.form.type);
					await api.setSetting({
						...this.form,
						type: tabObj.type
					});
					this.$message({
						type: "success",
						message: "保存成功",
					});
					this.handleClick({
						name: this.form.type
					});
				} catch (err) {
					console.error(err);
				} finally {
					this.loading = false;
				}
			},
		},
	};
</script>
<style lang="scss" scoped>
	::v-deep .el-tabs__content {
		padding: 0;
	}

	.btns {
		padding-top: 10px;
		text-align: center;

		.el-button {
			width: 250px !important;
			height: 50px;
		}
	}

	/* 确保字体选择下拉菜单中的字体显示正确 */
	.ql-font span[data-value="宋体"]::before {
		content: "宋体";
		font-family: "SimSun";
	}

	.ql-font span[data-value="黑体"]::before {
		content: "黑体";
		font-family: "SimHei";
	}

	.ql-font span[data-value="微软雅黑"]::before {
		content: "微软雅黑";
		font-family: "Microsoft YaHei";
	}
</style>