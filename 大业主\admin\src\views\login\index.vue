<template>
  <div class="login">
    <div class="login-container">
      <div class="login-left">
        <div class="brand">
          <img class="logoImg" src="../../assets/img/logo_big.png" alt="" />
          <h1 class="brand-title">{{ title }}</h1>
        </div>
      </div>
      <div class="login-right">
        <div class="login-form">
          <h2 class="form-title">账号登录</h2>
          <el-form
            :model="form"
            class="form"
            label-width="0"
            :rules="rules"
            ref="ruleForm"
          >
            <el-form-item prop="username">
              <el-input
                v-model.trim="form.username"
                prefix-icon="el-icon-user"
                placeholder="账号"
              />
            </el-form-item>
            <el-form-item prop="password">
              <el-input
                v-model.trim="form.password"
                prefix-icon="el-icon-message"
                placeholder="密码"
                show-password
                @keyup.enter.native="onSubmit"
              />
            </el-form-item>

            <el-button
              class="button"
              type="primary"
              @click="onSubmit()"
              :loading="loading"
              >登录</el-button
            >

            <!-- <div class="tips">未注册的手机号验证后自动创建办伴账户</div> -->
          </el-form>
        </div>
      </div>
    </div>
    <div class="login_foot">
      ©2025 办伴版权所有 [{{ BASE_URL }}] 【皖ICP备2024055573号-1】
      【皖公网安备34011102003618号】
    </div>
  </div>
</template>
<script>
import defaultSettings from "@/settings";
import { getSmsCode } from "@/api/user";
export default {
  data() {
    const validatorChecked = (rule, value, callback) => {
      if (value !== "1") {
        return callback(new Error("请阅读并勾选同意协议后登录"));
      }
      callback();
    };
    return {
      BASE_URL: process.env.VUE_APP_DOMAIN_URL,
      loading: false,
      redirect: undefined,
      form: {
        username: process.env.NODE_ENV == "development" ? "admin" : "",
        password: process.env.NODE_ENV == "development" ? "admin123" : "",
      },
      rules: {
        username: [
          { required: true, message: "请输入手机号", trigger: "blur" },
        ],
        password: [{ required: true, message: "请输入密码", trigger: "blur" }],
      },
    };
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true,
    },
  },
  created() {},
  mounted() {},
  computed: {
    title() {
      return defaultSettings.title;
    },
  },
  methods: {
    async onSubmit() {
      await this.$refs["ruleForm"].validate();
      console.error(this.form);
      this.loading = true;
      this.$store
        .dispatch("user/login", this.form)
        .then(() => {
          this.$router.push({ path: this.redirect || "/" });
          this.loading = false;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    async onSendcode() {
      if (this.codeNum > 0) return false;
      this.$refs["ruleForm"].validateField("telephone", async (vaildErr) => {
        console.log(vaildErr);
        if (!vaildErr) {
          //pass
          this.$refs["ruleForm"].clearValidate();
          try {
            const res = await getSmsCode(this.form.telephone);
            console.log(res);
            this.$message({
              message: "验证码发送成功",
              type: "success",
            });
          } catch (err) {
            console.error(err);
            this.$message({
              message: res.msg,
              type: "error",
            });
          } finally {
            this.codeFlag = true;
            this.codeNum = 60;
            this.codeTimer = setInterval(() => {
              if (this.codeNum === 0) {
                clearInterval(this.codeTimer);
              } else {
                this.codeNum--;
              }
            }, 1000);
          }
        }
        return false;
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.login {
  min-height: 100vh;
  background: #fff;
  display: flex;
  flex-direction: column;

  .login-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    position: relative;
    z-index: 1;
  }

  .login-left {
    width: 700px;
    padding-right: 100px;
    display: flex;
    align-items: center;
    justify-content: center;

    .brand {
      text-align: center;
      position: relative;

      &::after {
        content: "";
        position: absolute;
        right: -50px;
        top: 50%;
        transform: translateY(-50%);
        width: 1px;
        height: 200px;
        background: linear-gradient(
          to bottom,
          transparent,
          #e4e7ed,
          transparent
        );
      }

      .logoImg {
        width: 500px;
        height: 238px;
        margin-bottom: 24px;
        transition: transform 0.3s ease;

        &:hover {
          transform: scale(1.02);
        }
      }

      .brand-title {
        font-size: 32px;
        color: #1f2f3d;
        font-weight: 600;
        margin: 0;
        letter-spacing: 1px;
        background: linear-gradient(45deg, #1f2f3d, #409eff);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }

  .login-right {
    width: 400px;
    background: #fff;
    padding: 40px;
    border-radius: 8px;
    border: 1px solid #ebeef5;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.02);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.04);
      transform: translateY(-2px);
    }

    .login-form {
      .form-title {
        font-size: 24px;
        color: #1f2f3d;
        margin: 0 0 30px;
        font-weight: 600;
        text-align: center;
        position: relative;
        padding-bottom: 16px;

        &::after {
          content: "";
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 40px;
          height: 3px;
          background: #409eff;
          border-radius: 2px;
        }
      }

      .form {
        .button {
          width: 100%;
          height: 44px;
          font-size: 16px;
          border-radius: 6px;
          background: linear-gradient(45deg, #409eff, #36d1dc);
          border: none;
          margin-top: 10px;
          transition: all 0.3s;
          font-weight: 500;
          letter-spacing: 1px;

          &:hover {
            background: linear-gradient(45deg, #66b1ff, #5cd6d0);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
          }

          &:active {
            transform: translateY(0);
          }
        }

        ::v-deep .el-input {
          // margin-bottom: 24px;

          .el-input__inner {
            height: 44px;
            padding-left: 45px !important;
            border-radius: 6px;
            border: 1px solid #dcdfe6;
            transition: all 0.3s;
            font-size: 15px;

            &:focus {
              border-color: #409eff;
              box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
            }

            &:hover {
              border-color: #c0c4cc;
            }
          }

          .icon {
            font-size: 18px;
            color: #909399;
            padding-left: 14px;
          }

          .code {
            font-size: 14px;
            color: #409eff;
            padding-right: 8px;
            transition: all 0.3s;
            font-weight: 500;

            &:hover {
              color: #66b1ff;
            }

            &.disabled {
              color: #c0c4cc;
              cursor: not-allowed;

              &:hover {
                color: #c0c4cc;
              }
            }
          }
        }

        .tips {
          font-size: 14px;
          color: #909399;
          text-align: center;
          margin: 20px 0;
          line-height: 1.6;
        }

        .checkbox {
          margin: 24px 0;

          ::v-deep .el-form-item__content {
            display: flex;
            line-height: 22px;
            font-size: 14px;
            color: #606266;
          }

          a {
            color: #409eff;
            text-decoration: none;
            transition: all 0.3s;
            font-weight: 500;

            &:hover {
              color: #66b1ff;
              text-decoration: underline;
            }
          }

          p {
            padding-left: 12px;
            margin: 0;
          }
        }
      }
    }
  }

  &_foot {
    text-align: center;
    padding: 24px 0;
    color: #909399;
    font-size: 14px;
    border-top: 1px solid #ebeef5;
    background: #fafafa;
  }
}

// 响应式设计
@media screen and (max-width: 1200px) {
  .login {
    .login-container {
      padding: 40px 20px;
    }

    .login-left {
      width: 600px;
      padding-right: 60px;

      .brand {
        .logoImg {
          width: 400px;
          height: auto;
        }

        &::after {
          right: -30px;
          height: 160px;
        }
      }
    }
  }
}

@media screen and (max-width: 992px) {
  .login {
    .login-container {
      flex-direction: column;
    }

    .login-left {
      width: 100%;
      padding-right: 0;
      margin-bottom: 40px;

      .brand {
        &::after {
          display: none;
        }

        .logoImg {
          width: 300px;
          height: auto;
        }

        .brand-title {
          font-size: 28px;
        }
      }
    }

    .login-right {
      width: 100%;
      max-width: 400px;
    }
  }
}
</style>