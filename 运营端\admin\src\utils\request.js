/*
 *Author: CBB
 *Date: 2025-03-16 21:35:03
 *LastEditTime: 2025-04-01 19:55:06
 *LastEditors: CBB
 *Description:
 *FilePath: \admin\src\utils\request.js
 */
import axios from "axios";
import qs from "qs";
import { MessageBox, Message } from "element-ui";
import store from "@/store";
import { getToken } from "@/utils/auth";
import router from "@/router";
// create an axios service
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API,
  timeout: 5000, // request timeout
});

// request interceptor
service.interceptors.request.use(
  (config) => {
    if (config.method !== "get") {
      const requestType = config.requestType || "form";
      console.log(requestType);
      let ContentType = "";
      switch (requestType) {
        case "form-data":
          ContentType = "multipart/form-data";
          break;
        case "form":
          config.data = qs.stringify(config.data);
          ContentType = "application/x-www-form-urlencoded";
          break;
        case "json":
          ContentType = "application/json";
          break;
        default:
          ContentType = "application/json";
          break;
      }
      config.headers["Content-Type"] = ContentType;
    }

    const token = getToken();
    if (token) {
      config.headers.token = token;
    }

    return config;
  },
  (error) => {
    // do something with request error
    console.log(error); // for debug
    return Promise.reject(error);
  }
);

// response interceptor
service.interceptors.response.use(
  (response) => {
    const res = response.data;

    if (response.config.responseType == "blob") {
      return Promise.resolve(res);
    }

    // if the custom code is not 20000, it is judged as an error.
    if (res.code !== 200) {
      Message({
        message: res.message || "Error",
        type: "error",
        duration: 5 * 1000,
      });
      if (res.code === 401) {
        store.dispatch("user/resetToken");
        router.replace("/login");
        console.error("未授权，跳转到登录页面");
        return Promise.reject("未授权，请重新登录");
      }
      return Promise.reject(new Error(res.message || "Error"));
    }

    return Promise.resolve(res);
  },
  (error) => {
    console.error(error);

    console.log("err==>", error.response); // for debug
    const {
      response: {
        data: { message: msg },
      },
    } = error; // 获取后端返回的错误信息
    const { message } = error; // 获取服务器返回的错误信息
    console.log(msg);

    Message({
      message: msg || message,
      type: "error",
      duration: 5 * 1000,
    });
    return Promise.reject(error.response);
  }
);

export default service;
