import request from "@/utils/request";

export function getWithdrawal(data) {
  return request({
    url: "/admin/system/config/getWithdrawal",
    method: "GET",
    requestType: "json",
    data,
  });
}


export function getGoodsUserPercent(data) {
  return request({
    url: "/admin/system/config/getGoodsUserPercent",
    method: "GET",
    requestType: "json",
    data,
  });
}

export function getGoodsEndTime(data) {
  return request({
    url: "/admin/system/config/getGoodsEndTime",
    method: "GET",
    requestType: "json",
    data,
  });
}

export function getWyPlatformDividend(data) {
  return request({
    url: "/admin/system/config/getWyPlatformDividend",
    method: "GET",
    requestType: "json",
    data,
  });
}


export function getDefaultHeadUrl(data) {
  return request({
    url: "/admin/system/config/getDefaultHeadUrl",
    method: "GET",
    requestType: "json",
    data,
  });
}

export function getFirstBindPhonePoints(data) {
  return request({
    url: "/admin/system/config/getFirstBindPhonePoints",
    method: "GET",
    requestType: "json",
    data,
  });
}


export function getOrderPayTimeout(data) {
  return request({
    url: "/admin/system/config/getOrderPayTimeout",
    method: "GET",
    requestType: "json",
    data,
  });
}

export function getPageConfig(data) {
  return request({
    url: "/admin/system/config/getPageConfig",
    method: "GET",
    requestType: "json",
    data,
  });
}

// 商户月金额获取
export function getShPayMoney(data) {
  return request({
    url: "/admin/system/config/getShPayMoney",
    method: "GET",
    requestType: "json",
    data,
  });
}


export function setSystemConfig(data) {
  return request({
    url: "/admin/system/config/setSystemConfig",
    method: "POST",
    requestType: "json",
    data,
  });
}

export function getSignInRules(data) {
  return request({
    url: "/signIn/rule/getSignInRules",
    method: "get",
    requestType: "json",
    data,
  });
}


export function refreshSignInRules(data) {
  return request({
    url: "/signIn/rule/refreshSignInRules",
    method: "POST",
    requestType: "json",
    data,
  });
}

export function getWithdrawalTable(data) {
  return request({
    url: "/admin/system/config/getWithdrawalTable",
    method: "get",
    requestType: "json",
    data,
  });
}


export function setRuleConfig(data) {
  return request({
    url: "/admin/system/config/setSystemConfig",
    method: "POST",
    requestType: "json",
    data,
  });
}


export function getAllTypeList(data) {
  return request({
    url: "/service/type/getAllTypeList",
    method: "get",
    requestType: "json",
    data,
  });
}


export function addBaseType(data) {
  return request({
    url: "/service/type/addBaseType",
    method: "post",
    requestType: "json",
    data,
  });
}


export function modifyBaseType(data) {
  return request({
    url: "/service/type/modifyBaseType",
    method: "post",
    requestType: "json",
    data,
  });
}

export function addServiceType(data) {
  return request({
    url: "/service/type/addServiceType",
    method: "post",
    requestType: "json",
    data,
  });
}


export function modifyServiceType(data) {
  return request({
    url: "/service/type/modifyServiceType",
    method: "post",
    requestType: "json",
    data,
  });
}






// 1、获取说明
// /sw/admin/setting/getSetting
// get
// type:0-押金说明  1 关于我们

// 响应:
// {
// 	code:200,
// 	data:{
// 		id:id
// 		type:0-押金说明  1 关于我们
// 		content:内容
// 	}
// }

export function getSetting(data) {
  return request({
    url: "/admin/setting/getSetting",
    method: "get",
    params: data,
  });
}


// 2、设置说明
// /sw/admin/setting/setSetting
// post
// content-type:application/json

// 参数:{
// 	type:0-押金说明  1 关于我们
// 	content:内容
// }

export function setSetting(data) {
  return request({
    url: "/admin/setting/setSetting",
    method: "post",
    requestType: "json",
    data,
  });
}

//图片上传
export function uploadFile(data) {
  return request({
    baseURL: "/axy",
    url: "/file/uploadFile",
    method: "post",
    requestType: "form-data",
    data,
  });
}


// 获取通知列表
export function getAdviceByPage(params) {
  return request({
    url: "/admin/advice/getAdviceByPage",
    method: "get",
    params
  });
}

// 添加通知
export function addAdvice(data) {
  return request({
    url: "/admin/advice/addAdvice",
    method: "post",
    requestType: "json",
    data
  });
}

// 通过id获取通知
export function getAdviceById(params) {
  return request({
    url: "/admin/advice/getAdviceById",
    method: "get",
    params
  });
}

// 修改通知
export function modifyAdvice(data) {
  return request({
    url: "/admin/advice/modifyAdvice",
    method: "post",
    requestType: "json",
    data
  });
}

// 删除通知
export function removeAdvice(params) {
  return request({
    url: "/admin/advice/removeAdvice",
    method: "get",
    params
  });
}