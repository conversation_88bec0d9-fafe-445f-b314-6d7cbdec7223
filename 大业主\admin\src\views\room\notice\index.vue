<!--
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-06-10 15:21:28
 * @LastEditors: wangxingyue
 * @LastEditTime: 2025-07-21 14:42:55
 * @FilePath: \ban-ban\大业主\admin\src\views\room\notice\index.vue
-->
<template>
  <div class="app-container">
    <div class="addBtn">
      <el-button type="primary" @click="openActivity()">发布通知</el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      max-height="580"
      style="width: 100%"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
      @filter-change="handleFilterChange"
      :default-filter="activeFilters"
    >
      <el-table-column label="序号" type="index" width="50" align="center" />
      <el-table-column 
        label="归属项目" 
        align="center" 
        prop="projectName"
        :filters="projectNameOptions"
        filter-placement="bottom-end"
        :filter-multiple="false"
        column-key="projectName" 
      />
      <el-table-column label="标题" align="center" prop="title" />
      <el-table-column label="内容" align="center" prop="content" />
      <el-table-column label="发布时间" align="center" prop="publishTime">
        <template #default="{ row }">
          {{ parseTime(row.publishTime, "YYYY-MM-DD") }}
        </template>
      </el-table-column>
      <el-table-column label="发布人" align="center" prop="publisher" />

      <el-table-column label="操作" align="center">
        <template #default="{ row }">
          <el-button type="text" @click="openActivity(row)">修改</el-button>
          <el-button type="text" @click="delRow(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination">
      <el-pagination
        @size-change="fetchData"
        @current-change="fetchData"
        :current-page.sync="querySearch.pageNo"
        :page-sizes="[15, 30, 50, 100]"
        :page-size.sync="querySearch.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="querySearch.total"
      >
      </el-pagination>
    </div>

    <el-drawer
      :title="form.id ? '修改通知' : '发布通知'"
      :visible.sync="drawerShow"
      direction="rtl"
      size="800px"
    >
      <div class="drawer">
        <el-form
          ref="form"
          :model="form"
          label-width="100px"
          size="small"
          :rules="rules"
          :disabled="submitLoading"
        >
          <el-form-item label="选择项目：" prop="projectId">
            <el-select
              v-if="!form.id"
              v-model="form.projectId"
              placeholder="请选择项目名称"
              style="width: 100%"
            >
              <el-option
                v-for="item in projectList"
                :key="item.id"
                :label="item.projectName"
                :value="item.id"
              ></el-option>
            </el-select>
            <el-input
              v-else
              disabled
              v-model="form.projectName"
              type="textarea"
              placeholder="请输入内容"
              autosize
            ></el-input>
          </el-form-item>

          <el-form-item label="标题：" prop="title">
            <el-input
              v-model="form.title"
              placeholder="请输入活动标题"
            ></el-input>
          </el-form-item>

          <el-form-item label="内容：" prop="content">
            <el-input
              v-model="form.content"
              type="textarea"
              placeholder="请输入内容"
              autosize
            ></el-input>
          </el-form-item>

          <el-form-item label="发布人：" prop="publisher">
            <el-input
              v-model="form.publisher"
              placeholder="请输入发布人"
            ></el-input>
          </el-form-item>
        </el-form>
        <div class="btnBox">
          <el-button type="primary" @click="onSubmit()" :loading="submitLoading"
            >提交</el-button
          >
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import * as api from "@/api/room";
import { getAllProject } from "@/api/project";
export default {
  data() {
    return {
      list: null,
      listLoading: true,
      projectList: [], // 项目列表数据
      projectNameOptions: [], // 项目名称筛选选项
      activeFilters: {
        projectName: [] // 当前激活的筛选条件
      },
      querySearch: {
        pageNo: 1,
        pageSize: 50,
        total: 0,
        projectId: -1 // 默认查询全部项目
      },
      form: {
        projectId: "", // 项目ID
        title: "",
        content: "",
        publisher: "",
      },
      rules: {
        projectId: [
          { required: true, message: "请选择项目", trigger: "change" },
        ],
        title: [{ required: true, message: "请输入标题", trigger: "blur" }],
        content: [{ required: true, message: "请输入内容", trigger: "blur" }],
        publisher: [
          { required: true, message: "请输入发布人", trigger: "blur" },
        ],
      },
      submitLoading: false,
      drawerShow: false,
    };
  },
  created() {
    this.fetchData();
    this.getProjectList();
  },
  methods: {
    async fetchData() {
      try {
        this.listLoading = true;
        
        // 构建查询参数
        const queryParams = {
          pageNo: this.querySearch.pageNo,
          pageSize: this.querySearch.pageSize,
        };

        // 添加项目ID参数（-1表示全部项目，不传该参数）
        if (this.querySearch.projectId !== -1) {
          queryParams.projectId = this.querySearch.projectId;
          
          // 同步更新activeFilters中的projectName
          this.activeFilters.projectName = [this.querySearch.projectId];
        } else {
          // 如果查询全部项目，清空projectName筛选
          this.activeFilters.projectName = [];
        }

        console.log('查询参数:', queryParams);
        console.log('当前筛选状态:', this.activeFilters);
        
        const { data: res } = await api.getAdviceByPage(queryParams);
        console.log(res);
        const { data, pageNo, pageSize, totalElements } = res;
        this.list = data;
        this.querySearch = {
          ...this.querySearch,
          pageNo,
          pageSize,
          total: totalElements,
        };
      } catch (err) {
        console.error(err);
      } finally {
        this.listLoading = false;
      }
    },

    // 获取项目列表
    async getProjectList() {
      try {
        const { data } = await getAllProject();
        // 在数组首位添加"全部"选项
        const allProjects = [{ id: -1, projectName: "全部" }].concat(
          data || []
        );
        this.projectList = allProjects;
        
        // 设置项目筛选选项
        this.projectNameOptions = data.map(item => ({
          text: item.projectName,
          value: item.id
        }));
        
        console.log('项目列表:', this.projectList);
        console.log('项目筛选选项:', this.projectNameOptions);
      } catch (err) {
        console.error(err);
      }
    },
    
    // 处理表格筛选变化
    handleFilterChange(filters) {
      console.log('表格筛选变化:', filters);
      
      // 处理项目名称筛选
      if ('projectName' in filters) {
        // 更新activeFilters中的projectName
        this.activeFilters.projectName = filters.projectName || [];
        
        // 如果是项目名称筛选发生变化
        if (filters.projectName && filters.projectName.length > 0) {
          this.querySearch.projectId = filters.projectName[0];
        } else {
          this.querySearch.projectId = -1; // 如果取消选中，则重置为全部项目
        }
      }

      console.log('筛选条件:', {
        项目ID: this.querySearch.projectId
      });

      // 重置到第一页
      this.querySearch.pageNo = 1;
      this.fetchData();
    },

    async openActivity(row = {}) {
      if (row.id) {
        //编辑
        this.form = {
          ...row,
          projectId: row.projectId || row.project?.id, // 确保项目ID正确回显
        };
      } else {
        //新增
        this.form = {
          projectId: "",
          title: "",
          content: "",
          publisher: "",
        };
      }
      this.drawerShow = true;
    },
    async onSubmit() {
      await this.$refs.form.validate();
      const postData = {
        ...this.form,
      };
      console.log(postData);

      try {
        this.submitLoading = true;
        if (postData.id) {
          //编辑
          await api.modifyAdvice(postData);
        } else {
          //新增
          await api.addAdvice(postData);
        }
        this.$message({
          type: "success",
          message: "提交成功",
        });
        this.drawerShow = false;
        this.fetchData();
      } catch (err) {
        console.error(err);
      } finally {
        this.submitLoading = false;
      }
    },
    async delRow(row) {
      await this.$confirm(`确认删除【${row.title}】通知吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      });
      await api.removeAdvice({ id: row.id });
      this.$message({
        type: "success",
        message: "操作成功",
      });
      this.fetchData();
    },
  },
};
</script>

<style scoped lang="scss">
.pagination {
  padding-top: 20px;
  text-align: right;
}
.drawer {
  padding: 0 20px;
}
.btnBox {
  padding-left: 100px;
}
.addBtn {
  padding-bottom: 20px;
  // padding-left: 10px;
}
::v-deep .hide .el-upload--picture-card {
  display: none;
}
</style>