import request from "@/utils/request";

export function getSmsCode(telephone) {
  return request({
    url: "/auth/getPhone",
    method: "post",
    data: {
      telephone,
    },
  });
}

/**
 *
 * telephone=*********** 必填
code = 123456 必填
checked = 1   隐私协议是否选中  必填 0 未选中 1选中

@returns  
 "token": "Bearer 1901175636886687744", //token
 "status": 3 //账户状态
 */
export function login(data) {
  return request({
    url: "/wy/account/login",
    method: "post",
    requestType: "json",
    data,
  });
}
// 业主端用户密码修改
export function modifyPassword(data) {
  return request({
    url: "/wy/accountInfo/modifyPassword",
    method: "post",
    requestType: "json",
    data,
  });
}

// 退出登录
export function logout() {
  return request({
    url: "/wy/account/logout",
    method: "get",
  });
}

//获取小区id与状态
export function getAccountInfo() {
  return request({
    url: "/auth/getAccountInfo",
    method: "get",
  });
}
