<template>
  <div class="">
    <el-table
      v-loading="listLoading"
      :data="list"
      max-height="580"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
      <el-table-column label="物业标识符" align="center" prop="openKey" />
      <el-table-column label="提现金额" align="center" prop="amount" />
      <el-table-column label="状态" align="center" prop="status">
          <template #default="{ row }">
            <el-tag :type="statusFilter(row.status).type">{{
              statusFilter(row.status).name
            }}</el-tag>
          </template>
        </el-table-column>
      <el-table-column label="申请时间" align="center" prop="applyTime">
        <template #default="{ row }">{{
          parseTime(row.applyTime, "YYYY-MM-DD")
        }}</template>
      </el-table-column>
      <el-table-column label="结束时间" align="center" prop="endTime" />
    </el-table>

    <div class="pagination">
      <el-pagination
        @size-change="fetchData"
        @current-change="fetchData"
        :current-page.sync="querySearch.pageNo"
        :page-sizes="[15, 30, 50, 100]"
        :page-size.sync="querySearch.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="querySearch.total"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import * as api from "@/api/landlord";
export default {
  name: "LandlordwithdrawalRecord",
  data() {
    return {
      list: null,
      listLoading: true,
      querySearch: {
        pageNo: 1,
        pageSize: 50,
        total: 0,
      },

      statusOptions: [
        {
          name: "全部",
          value: "-1",
          type: "success",
        },
        {
          name: "申请",
          value: "0",
          type: "info",
        },
        {
          name: "拒绝",
          value: "1",
          type: "success",
        },
        {
          name: "成功",
          value: "2",
          type: "success",
        },
      ],
    };
  },
  computed: {
    openKey() {
      return this.$route.params.openKey || "";
    },
  },
  mounted() {
    this.fetchData();
  },
  activated() {
    this.fetchData();
  },
  methods: {
    statusFilter(status) {
      return (
        this.statusOptions.find((e) => e.value == status) || {
          name: "未知",
          value: "",
          type: "",
        }
      );
    },

    async fetchData() {
      try {
        this.listLoading = true;
        const { data: res } = await api.getWithdrawalList({
          ...this.querySearch,
          openKey: this.openKey,
        });
        const { data, pageNo, pageSize, totalElements } = res;
        this.list = data;
        this.querySearch = {
          pageNo,
          pageSize,
          total: totalElements,
          status: this.querySearch.status,
        };
      } catch (err) {
        console.error(err);
      } finally {
        this.listLoading = false;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.pagination {
  padding-top: 20px;
  text-align: right;
}
</style>