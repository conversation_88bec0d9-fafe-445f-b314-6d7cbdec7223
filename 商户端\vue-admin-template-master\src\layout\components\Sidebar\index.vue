<template>
  <div :class="{ 'has-logo': showLogo }">
    <logo v-if="showLogo" :collapse="isCollapse" />
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu :default-active="activeMenu" :collapse="isCollapse" :background-color="variables.menuBg"
        :text-color="variables.menuText" :unique-opened="false" :active-text-color="variables.menuActiveText"
        :collapse-transition="false" :default-openeds="defaultOpenedsIndexs" mode="vertical" router
        @select="handleMenuSelect">
        <sidebar-item v-for="route in routes" :key="route.path" :item="route" :base-path="route.path" />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Logo from './Logo'
import SidebarItem from './SidebarItem'
import variables from '@/styles/variables.scss'

export default {
  components: { SidebarItem, Logo },
  computed: {
    ...mapGetters([
      'sidebar'
    ]),
    routes () {
      return this.$router.options.routes
    },
    defaultOpenedsIndexs () {
      return this.routes.map((e, i) => e.path)
    },
    activeMenu () {
      const route = this.$route
      const { meta, path } = route
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return path
    },
    showLogo () {
      return this.$store.state.settings.sidebarLogo
    },
    variables () {
      return variables
    },
    isCollapse () {
      return !this.sidebar.opened
    }
  },
  methods: {
    handleMenuSelect(index, indexPath) {
      console.log('Menu selected:', index, indexPath);

      // 获取当前路由
      const currentPath = this.$route.path;

      // 如果点击的是当前路由，则刷新页面
      if (currentPath === index) {
        this.refreshCurrentPage(index);
      }
    },

    refreshCurrentPage(targetPath) {
      // 使用refresh页面进行刷新
      this.$router.replace({
        path: '/refresh',
        query: {
          redirect: targetPath,
          timestamp: Date.now() // 添加时间戳确保每次都是新的路由
        }
      });
    }
  }
}
</script>
