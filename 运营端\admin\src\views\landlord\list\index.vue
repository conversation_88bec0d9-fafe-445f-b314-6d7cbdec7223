<!--
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-05-27 10:49:34
 * @LastEditors: wangxingyue
 * @LastEditTime: 2025-07-25 15:15:41
 * @FilePath: \ban-ban\运营端\admin\src\views\landlord\list\index.vue
-->

<template>
  <div class="app-container">
    <template v-if="isDetail">
      <!-- 添加router-view用于显示子路由内容 -->
      <router-view />
    </template>
    <template v-else>
      <el-form inline>
        <el-form-item label="状态：">
          <el-select v-model="querySearch.status" placeholder="请选择用户状态">
            <el-option
              v-for="(item, index) in statusOptions"
              :key="index"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        &nbsp;
        <el-form-item label="姓名：">
          <el-input
            v-model="querySearch.name"
            placeholder="请输入姓名"
            clearable
          />
        </el-form-item>
        &nbsp;
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="fetchData()">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetSearch()"
              >重置</el-button
            >
          <el-button type="success" icon="el-icon-plus" @click="handleAdd">添加业主账号</el-button>
        </el-form-item>
      </el-form>
      <el-table
        v-loading="listLoading"
        :data="list"
        max-height="580"
        element-loading-text="Loading"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="ID" align="center" prop="id" width="100" />
        <el-table-column label="姓名" align="center" prop="name" width="150" />
        <el-table-column label="联系电话" align="center" prop="telephone" width="150" />
        <el-table-column label="账号" align="center" prop="account" />
        <el-table-column label="密码" align="center" prop="password" />
        <el-table-column label="描述" align="center" prop="description" />
        <el-table-column label="状态" align="center" prop="status" width="150">
          <!-- 状态：0 禁用  1 启用 -->
          <template #default="{ row }">
            <el-tag :type="statusFilter(row.status).type">{{
              statusFilter(row.status).name
            }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="300" align="center">
          <template #default="{ row }">
            <el-button type="text" @click="openLandlordInfoList(row)"
              >房东详情</el-button
            >
            <el-button type="text" @click="handleEdit(row)">修改</el-button>
            <!-- <el-button
              type="text"
              :class="row.status == '0' ? 'success-text' : 'danger-text'"
              @click="handleStatusChange(row)"
            >
              {{ row.status == "0" ? "启用" : "禁用" }}
            </el-button>
            <el-button
              type="text"
              class="danger-text"
              @click="handleDelete(row)"
              >删除账号</el-button
            > -->
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          @size-change="fetchData"
          @current-change="fetchData"
          :current-page.sync="querySearch.pageNo"
          :page-sizes="[15, 30, 50, 100]"
          :page-size.sync="querySearch.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="querySearch.total"
        >
        </el-pagination>
      </div>

      <!-- 添加业主账号对话框 -->
      <el-dialog
        :title="dialogTitle"
        :visible.sync="dialogVisible"
        width="500px"
        @close="resetForm('accountForm')"
      >
        <el-form
          :model="accountForm"
          :rules="accountFormRules"
          ref="accountForm"
          label-width="100px"
        >
          <el-form-item label="姓名" prop="name">
            <el-input
              v-model="accountForm.name"
              placeholder="请输入姓名"
            ></el-input>
          </el-form-item>
          <el-form-item label="联系电话" prop="telephone">
            <el-input
              v-model="accountForm.telephone"
              placeholder="请输入联系电话"
            ></el-input>
          </el-form-item>
          <el-form-item label="账号" prop="account" v-if="dialogType === 'add'">
            <el-input
              v-model="accountForm.account"
              placeholder="请输入账号"
            ></el-input>
          </el-form-item>
          <el-form-item label="密码" prop="password">
            <el-input
              v-model="accountForm.password"
              placeholder="请输入密码"
              show-password
            ></el-input>
          </el-form-item>
          <el-form-item label="描述" prop="description">
            <el-input
              v-model="accountForm.description"
              type="textarea"
              placeholder="请输入描述"
            ></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button
            type="primary"
            @click="submitForm('accountForm')"
            :loading="submitLoading"
            >确 定</el-button
          >
        </div>
      </el-dialog>
    </template>
  </div>
</template>

<script>
import * as api from "@/api/user";
export default {
  name: "Landlordlist",
  data() {
    return {
      list: null,
      listLoading: true,
      querySearch: {
        pageNo: 1,
        pageSize: 50,
        status: "", // 用户状态
        name: "", // 用户名
        total: 0,
      },

      statusOptions: [
        {
          name: "全部",
          value: "2",
          type: "success",
        },
        {
          name: "禁用",
          value: "0",
          type: "danger",
        },
        {
          name: "启用",
          value: "1",
          type: "success",
        },
      ],

      // 表单相关数据
      dialogVisible: false,
      dialogTitle: "添加业主账号",
      dialogType: "add", // add 或 edit
      submitLoading: false,
      accountForm: {
        id: "", // 修改时使用
        name: "",
        telephone: "",
        account: "",
        password: "",
        description: "",
      },
      accountFormRules: {
        name: [{ required: true, message: "请输入姓名", trigger: "blur" }],
        telephone: [
          { required: true, message: "请输入联系电话", trigger: "blur" },
        ],
        account: [{ required: true, message: "请输入账号", trigger: "blur" }],
        password: [{ required: true, message: "请输入密码", trigger: "blur" }],
      },

      // 删除修改用户信息相关数据，使用上面的数据代替
    };
  },
  computed: {
    isDetail() {
      return this.$route.name !== "Landlordlist";
    },
  },
  mounted() {
    this.fetchData();
  },
  activated() {
    this.fetchData();
  },
  methods: {
    statusFilter(status) {
      return (
        this.statusOptions.find((e) => e.value == status) || {
          name: "未知",
          value: "",
          type: "",
        }
      );
    },

    async fetchData() {
      try {
        this.listLoading = true;
        const { data: res } = await api.findAccountByPage(this.querySearch);
        const { data, pageNo, pageSize, totalElements } = res;
        this.list = data;
        this.querySearch = {
          pageNo,
          pageSize,
          total: totalElements,
          status: this.querySearch.status,
          name: this.querySearch.name,
        };
      } catch (err) {
        console.error(err);
      } finally {
        this.listLoading = false;
      }
    },
    // 重置搜索条件
    resetSearch() {
      this.querySearch = {
        pageNo: 1,
        pageSize: 50,
        status: "",
        name: "",
        total: 0,
      };
      // 重置到第一页并获取数据
      this.querySearch.pageNo = 1;
      this.fetchData();
    },


    // 添加业主账号相关方法
    handleAdd() {
      this.dialogType = "add";
      this.dialogTitle = "添加业主账号";
      this.dialogVisible = true;
      this.resetForm("accountForm");
    },

    resetForm(formName) {
      this.$refs[formName] && this.$refs[formName].resetFields();
      this.accountForm = {
        id: "",
        name: "",
        telephone: "",
        account: "",
        password: "",
        description: "",
      };
    },

    async submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          try {
            this.submitLoading = true;
            if (this.dialogType === "add") {
              await api.addAccount(this.accountForm);
              this.$message.success("添加业主账号成功");
            } else {
              await api.modifyAccount(this.accountForm);
              this.$message.success("修改用户信息成功");
            }
            this.dialogVisible = false;
            this.fetchData(); // 刷新列表
          } catch (err) {
            console.error(err);
          } finally {
            this.submitLoading = false;
          }
        } else {
          return false;
        }
      });
    },

    // 修改用户信息相关方法
    handleEdit(row) {
      this.dialogType = "edit";
      this.dialogTitle = "修改用户信息";
      this.accountForm = {
        id: row.id,
        name: row.name,
        telephone: row.telephone,
        // password: row.password,
        password: '',
        description: row.description,
      };
      this.dialogVisible = true;
    },

    // // 处理启用/禁用用户状态
    // async handleStatusChange(row) {
    //   try {
    //     await this.$confirm(
    //       `确定要${row.status == "0" ? "启用" : "禁用"}该用户吗?`,
    //       "提示",
    //       {
    //         confirmButtonText: "确定",
    //         cancelButtonText: "取消",
    //         type: "warning",
    //       }
    //     );

    //     this.listLoading = true;
    //     await api.enableOrDisableAccount(row.id);
    //     this.$message.success(
    //       `${row.status == "0" ? "已启用" : "已禁用"}该用户`
    //     );
    //     this.fetchData(); // 刷新列表数据
    //   } catch (error) {
    //     if (error !== "cancel") {
    //       this.$message.error(`操作失败：${error.message || "未知错误"}`);
    //     }
    //   } finally {
    //     this.listLoading = false;
    //   }
    // },
    // // 删除用户
    // async handleDelete(row) {
    //   try {
    //     await this.$confirm("确定要删除该业主账号吗?", "提示", {
    //       confirmButtonText: "确定",
    //       cancelButtonText: "取消",
    //       type: "warning",
    //     });

    //     this.listLoading = true;
    //     await api.deleteAccount(row.id);
    //     this.$message.success("已删除");
    //     this.fetchData(); // 刷新列表数据
    //   } catch (error) {
    //     if (error !== "cancel") {
    //       this.$message.error(`删除失败：${error.message || "未知错误"}`);
    //     }
    //   } finally {
    //     this.listLoading = false;
    //   }
    // },

    openLandlordInfoList({ id }) {
      this.$router.push({
        name: "LandlordInfoList",
        params: {
          userId: id,
        },
      });
    },
  },
};
</script>

<style scoped lang="scss">
.pagination {
  padding-top: 20px;
  text-align: right;
}

.success-text {
  color: #67c23a;
}

.danger-text {
  color: #f56c6c;
}
</style>
