<!--
 * @Author: CBB
 * @Date: 2025-03-18 19:00:23
 * @LastEditTime: 2025-04-01 20:50:40
 * @LastEditors: CBB
 * @Description:
 * @FilePath: \admin\src\layout\components\Sidebar\Item.vue
-->
<script>
import stepEnum from "@/plugins/stepEnum";
import { mapState } from "vuex";

export default {
  name: "MenuItem",
  functional: false,
  props: {
    icon: {
      type: String,
      default: "",
    },
    title: {
      type: String,
      default: "",
    },
    requiredEdit: {
      type: Boolean,
      default: false,
    },
    step: {
      type: Number,
      default: null,
    },
    name: {
      type: String,
      default: "",
    },
    badgeCount: {
      type: Number,
      default: 0,
    },
  },
  computed: {
    ...mapState({
      status: state => state.user.userInfo.status
    })
  },
  render(h) {
    const { icon, title, requiredEdit, name, step, badgeCount } = this;
    let nowPage = {};
    for (const i in stepEnum) {
      if (Object.prototype.hasOwnProperty.call(stepEnum, i)) {
        const e = stepEnum[i];
        if (e.name === name) {
          nowPage = e;
        }
      }
    }
    const vnodes = [];
    // if (nowPage?.title && step < 8 && nowPage?.step >= step && requiredEdit && this.status === 0) {
    if (nowPage?.title && requiredEdit && this.status !== 1) {
      vnodes.push(
        <el-tag class="item-tag" size="mini" effect="plain" type="warning">
          {"必填"}
        </el-tag>
      );
    }

    if (icon) {
      if (icon.includes("el-icon")) {
        vnodes.push(<i class={[icon, "sub-el-icon"]} />);
      } else if (icon.includes("png")) {
        vnodes.push(<img class={["sub-el-img"]} src={icon} />);
      } else {
        vnodes.push(<svg-icon icon-class={icon} />);
      }
    }

    if (title) {
      const titleWrapper = [];
      titleWrapper.push(<span slot="title">{title}</span>);

      // 添加红色角标
      if (badgeCount > 0) {
        titleWrapper.push(
          <el-badge
            value={badgeCount}
            type="danger"
            class="badge-wrapper"
          />
        );
      }

      vnodes.push(<div class="title-wrapper">{titleWrapper}</div>);
    }
    return <div class="menu-item-wrapper">{vnodes}</div>;
  },
};
</script>

<style scoped>
.sub-el-icon {
  color: currentColor;
  width: 1em;
  height: 1em;
}

.sub-el-img {
  width: 25px;
  height: 25px;
  object-fit: contain;
  display: inline-block;
  margin-right: 10px;
}

.item-tag {
  transform: scale(0.9);
  margin-right: 2px;
  vertical-align: middle;
}

.menu-item-wrapper {
  display: inline-flex;
  align-items: center;
  width: 100%;
}

.title-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.badge-wrapper {
  margin-left: auto;
}

.badge-wrapper >>> .el-badge__content {
  background-color: #f56c6c;
  border-color: #f56c6c;
}
</style>
