<!--
 * @Author: CBB
 * @Date: 2025-03-16 21:35:03
 * @LastEditTime: 2025-03-18 01:50:07
 * @LastEditors: CBB
 * @Description:
 * @FilePath: \安小页\商户端\vue-admin-template-master\src\layout\components\AppMain.vue
-->
<template>
  <section class="app-main">
    <el-card shadow="always">
      <template #header>
        <div class="title">
          <h5>{{ name }}</h5>
        </div>
      </template>
      <transition name="fade-transform" mode="out-in">
        <router-view :key="key" />
      </transition>
    </el-card>
  </section>
</template>

<script>
export default {
  name: 'AppMain',
  computed: {
    key () {
      return this.$route.path
    },
    name () {
      return this.$route.meta?.title || ''
    }

  }
}
</script>

<style scoped lang="scss">
.app-main {
  /*50 = navbar  */
  min-height: calc(100vh - 50px);
  width: 100%;
  position: relative;
  overflow: hidden;
  padding: 20px;
}



.fixed-header+.app-main {
  padding-top: 70px;
}

::v-deep .el-card__header {
  background: #F5F9FF;

  .title {
    h5 {
      font-size: 18px;
    }
  }
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 15px;
  }
}
</style>
