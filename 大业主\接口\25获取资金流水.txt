1、获取资金流水

/sw/wy/withdrawal/apply/getWyPayHistoryDetail

post
{
	
  "pageNo": "页码",
  "pageSize": "页大小",
  "projectId": "所属项目 -1全部",
  "roomNo": "房屋号",
  "type": "-1 全部 0 房租 1 电费  3 提现", 

}



响应:
{
	code: 业务响应码 200 500...  
	message: 这个是异常的时候的消息提示
	data:{
		pageNo: '第几页'
		pageSize:'一页多少页'
		totalPages:'总页数'
		totalElements:'总数据量'
		data:[

{
  "billDate": "日期",
  "amount": "支付金额",
  "surplus": "余额",
  "projectName": "项目名称",
  "roomNo": "房号",
  "type": "0 房租 1 电费 2 水费 3 提现",
  "payTime": "支付时间",
  "payType": "0 线下 1 线上 2 对公"
}
			]
	}
}



