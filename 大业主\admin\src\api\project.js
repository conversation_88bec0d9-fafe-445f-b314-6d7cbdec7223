/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-07-17 14:28:26
 * @LastEditors: wang<PERSON>yue
 * @LastEditTime: 2025-07-17 14:28:57
 * @FilePath: \ban-ban\大业主\admin\src\api\project.js
 */
import request from "@/utils/request";

// 获取所有项目
export function getAllProject() {
    return request({
        url: "/wy/openProject/getAllProject",
        method: "get"
    });
}

// 获取项目列表
export function getProjectPage(data) {
    return request({
        url: "/wy/openProject/getByPage",
        method: "post",
        requestType: "json",
        data
    });
}

// 创建项目
export function addProject(data) {
    return request({
        url: "/wy/openProject/addProject",
        method: "post",
        requestType: "json",
        data
    });
}

// 修改项目
export function modifyProject(data) {
    return request({
        url: "/wy/openProject/modifyProject",
        method: "post",
        requestType: "json",
        data
    });
}