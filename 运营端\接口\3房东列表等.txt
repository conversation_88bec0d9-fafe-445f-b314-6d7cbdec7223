1、房东列表
/sw/admin/wy/info/getOwnerList
post
content-type:application/json
参数:
{
	pageNo:
	pageSize:

}
响应:
{
	code: 业务响应码 200 500...  
	message: 这个是异常的时候的消息提示
	data:{
		pageNo: '第几页'
		pageSize:'一页多少页'
		totalPages:'总页数'
		totalElements:'总数据量'
		data:[
		{
  "id": "公司ID",
  "openKey": "大房东标志",
  "companyName": "公司名称",
  "responsiblePerson": "负责人/联系人姓名(对应数据库 responsible_person 字段)",
  "responsibleTelephone": "负责人联系电话(对应数据库 responsible_telephone 字段)",
  "createTime": "公司注册/创建时间(对应数据库 create_time 字段)",
  "totalRooms": "总房间数(计算字段：所有关联的项目房间数)",
  "rentedRooms": "已出租房间数(status = 1 的房间数量)",
  "availableRooms": "待出租房间数(status = 0 的房间数量)",
  "totalIncome": "收款总额(元)(已支付的租金账单总额，payment_status = 1 且 pay_type = 0)",
  "totalWithdrawal": "提现总额(元)(已完成的提现申请总额，status = 2)",
  "balance": "账户余额(元)(计算字段：totalIncome - totalWithdrawal) 注意：此字段需要业务逻辑计算，构造函数中不初始化"
}]
	}
}

2、商户列表
/sw/admin/sh/getMerchantList

post
content-type:application/json
参数:
{
	pageNo:
	pageSize:

}
响应:
{
	code: 业务响应码 200 500...  
	message: 这个是异常的时候的消息提示
	data:{
		pageNo: '第几页'
		pageSize:'一页多少页'
		totalPages:'总页数'
		totalElements:'总数据量'
		data:[
		{

        id:id,
        createTime:注册时间,
  "shopName": "店铺名称",
  "typeName":类型名称,
  "mainBusiness": "主营业务",
  "description": "店铺简介",
  "address": "店铺地址",
  "imageUrl": "图片URL列表",
  "contactPerson": "联系人姓名",
  "phone": "联系电话",
  "businessLicenseUrl": "营业执照图片URL",
  "openKey": "商户标识",
  "paymentTime": "付费日",
  "expiredTime": "到期日",
  "totalAmount": "总付费",
  "status": "状态  1 在线  0 下线"
}]
	}
}


3、订单列表
/sw/admin/ds/getAllOrder
post
content-type:application/json
参数:
{
	pageNo:
	pageSize:

}
响应:
{
	code: 业务响应码 200 500...  
	message: 这个是异常的时候的消息提示
	data:{
		pageNo: '第几页'
		pageSize:'一页多少页'
		totalPages:'总页数'
		totalElements:'总数据量'
		data:[
		{
  "no": "订单编号唯一",
  "telephone": "手机号码",
  "modifyTime": "订单最后一次更新时间",
  "orderId": "订单唯一参数",
  "channelId": "渠道 ID：1=淘宝,2=京东,3=拼多多,4=美团",
  "sid": "业务编码 对应微信用户id",
  "thirdOrderId": "第三方渠道内部订ID",
  "payTime": "订单支付时间",
  "finishTime": "订单完成时间",
  "status": "订单状态：1=待付款，2=已付款，3=已完成，4=失效",
  "goodsTitle": "商品标题",
  "goodsId": "商品 ID",
  "goodsNum": "商品数量",
  "goodsPrice": "商品价格",
  "goodsImage": "商品封面",
  "commissionRate": "佣金比",
  "estimateCosPrice": "预计计佣价格",
  "estimateTotalMoney": "预计佣金金额",
  "estimateTotalFee": "预计佣金积分",
  "actualTotalMoney": "真实佣金金额",
  "actualTotalFee": "真实佣金价格",
  "estimateCosFee": "预计计佣金价格 积分计算",
  "estimateMoney": "预计用户佣金金额计算",
  "estimateFee": "预计佣金 积分计算",
  "estimatePlatformMoney": "预计平台佣金 金额计算",
  "estimatePlatformFee": "预计平台佣金",
  "actualCosPrice": "真实即用价格",
  "actualCosFee": "实际计佣价格 积分计算",
  "actualMoney": "真实用户佣金 金额计算",
  "actualFee": "实际用户佣金 积分计算",
  "actualPlatformMoney": "真实平台佣金金额计算",
  "actualPlatformFee": "实际平台佣金积分计算",
  "endStatus": "0 没有结算 1 结算了",
  "endTime": "结算时间",
  "percent": "订单获取时佣金比例",
  "openKey": "所属物业",
  "generateDate": "生成日期"
}]
	}
}


4、提现记录
/sw/admin/wy/info/getWithdrawalList
post
content-type:application/json
参数:
{
	pageNo:
	pageSize:
	openKey:大房东的标识

}
响应:
{
	code: 业务响应码 200 500...  
	message: 这个是异常的时候的消息提示
	data:{
		pageNo: '第几页'
		pageSize:'一页多少页'
		totalPages:'总页数'
		totalElements:'总数据量'
		data:[
		{
  "openKey": "物业标识符",
  "amount": "提现金额",
  "applyTime": "申请时间",
  "endTime": "结束时间",
  "status": "状态 0 申请  1 拒绝  2 成功"
}]
	}
}
