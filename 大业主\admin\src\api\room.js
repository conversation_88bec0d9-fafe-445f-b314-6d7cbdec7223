import request from "@/utils/request";

// 获取房屋列表
export function getRoomList(data) {
  // 确保API调用正确处理筛选参数
  return request({
    url: "/wy/project/room/getProjectRoom",
    method: "post",
    requestType: "json",
    data,
  });
}

// 修改房屋信息
export function modifyProjectRoom(data) {
  return request({
    url: "/wy/project/room/modifyProjectRoom",
    method: "post",
    requestType: "json",
    data,
  });
}

// 获取房屋租赁历史列表
export function getLeaseHistory(data) {
  return request({
    url: "/wy/lease/info/getLeaseHistory",
    method: "post",
    requestType: "json",
    data
  });
}

// 根据id获取房屋信息
export function getProjectRoomInfo(roomId) {
  return request({
    url: "/wy/project/room/getProjectRoomInfo",
    method: "get",
    params: { roomId }
  });
}

// 添加租赁人员信息
export function addLeaseInfo(data) {
  return request({
    url: "/wy/lease/info/addLeaseInfo",
    method: "post",
    requestType: "json",
    data
  });
}

// 获取当前租赁关系
export function getLeaseInfo(roomId) {
  return request({
    url: "/wy/lease/info/getLeaseInfo",
    method: "get",
    params: { roomId }
  });
}

// 修改租赁人员信息
export function modifyLeaseInfo(data) {
  return request({
    url: "/wy/lease/info/modifyLeaseInfo",
    method: "post",
    requestType: "json",
    data
  });
}

// 终止租赁关系
export function stopLease(data) {
  return request({
    url: "/wy/lease/info/stopLease",
    method: "post",
    requestType: "json",
    data
  });
}

// 获取账单列表
export function getRentBillsByPage(data) {
  return request({
    url: "/wy/rent/bill/getRentBillsByPage",
    method: "post",
    requestType: "json",
    data
  });
}

// 支付账单
export function payRentBill(data) {
  return request({
    url: "/wy/rent/bill/payRentBills",
    method: "post",
    requestType: "json",
    data
  });
}

// 获取房屋金额统计数据
export function getRoomAmount(roomId) {
  return request({
    url: "/wy/lease/info/getRoomAmount",
    method: "get",
    params: { roomId }
  });
}

// 获取房屋修改历史列表
export function getRoomHistories(data) {
  return request({
    url: "/wy/room/history/getRoomHistories",
    method: "post",
    requestType: "json",
    data
  });
}


// 获取通知列表
export function getAdviceByPage(params) {
  return request({
    url: "/wy/advice/getAdviceByPage",
    method: "get",
    params
  });
}

// 添加通知
export function addAdvice(data) {
  return request({
    url: "/wy/advice/addAdvice",
    method: "post",
    requestType: "json",
    data
  });
}

// 通过id获取通知
export function getAdviceById(params) {
  return request({
    url: "/wy/advice/getAdviceById",
    method: "get",
    params
  });
}

// 修改通知
export function modifyAdvice(data) {
  return request({
    url: "/wy/advice/modifyAdvice",
    method: "post",
    requestType: "json",
    data
  });
}

// 删除通知
export function removeAdvice(params) {
  return request({
    url: "/wy/advice/removeAdvice",
    method: "get",
    params
  });
}

// 获取项目的用电量
export function getByPage(data) {
  return request({
    url: "/wy/projectEle/used/getByPage",
    method: "post",
    requestType: "json",
    data
  });
}

// 获取房屋管理员列表
export function getAllManager(roomId) {
  return request({
    url: "/wy/room/manager/getAllManager",
    method: "get",
    params: { roomId }
  });
}

// 添加房屋管理员
export function addRoomManager(data) {
  return request({
    url: "/wy/room/manager/addRoomManager",
    method: "post",
    requestType: "json",
    data
  });
}

// 删除房屋管理员
export function removeRoomManager(managerId) {
  return request({
    url: "/wy/room/manager/removeRoomManager",
    method: "get",
    params: { managerId }
  });
}

// 添加房间
export function addProjectRoom(data) {
  return request({
    url: "/wy/project/room/addProjectRoom",
    method: "post",
    requestType: "json",
    data
  });
}

// 获取预警历史数据
export function getAlertHistory(data) {
  return request({
    url: "/wy/alert/history/findByReportId",
    method: "post",
    requestType: "json",
    data
  });
}

// 获取房间用电量数据
export function getRoomElectricityUsage(data) {
  return request({
    url: "/wy/ele/consump/findByPage",
    method: "post",
    requestType: "json",
    data
  });
}

// 获取充值记录
export function getRechargeHistory(data) {
  return request({
    url: "/wy/ele/pay/history/findByPage",
    method: "post",
    requestType: "json",
    data
  });
}

// 获取清零历史
export function getClearHistory(data) {
  return request({
    url: "/wy/ele/meter/getClearHistory",
    method: "post",
    requestType: "json",
    data
  });
}

// 电表清零操作
export function clearMeter(roomId) {
  return request({
    url: "/wy/ele/meter/clearMeter",
    method: "get",
    params: { roomId }
  });
}

// 电表开户操作
export function openAccount(roomId) {
  return request({
    url: "/wy/ele/meter/openAccount",
    method: "get",
    params: { roomId }
  });
}

// 获取开户历史记录
export function getOpenAccountHistory(data) {
  return request({
    url: "/wy/ele/meter/getOpenAccountHistory",
    method: "post",
    requestType: "json",
    data
  });
}

// 获取房间基本电量使用信息
export function getRoomElectricityInfo(params) {
  return request({
    url: "/wy/ele/consump/getRoomInfo",
    method: "get",
    params
  });
}

