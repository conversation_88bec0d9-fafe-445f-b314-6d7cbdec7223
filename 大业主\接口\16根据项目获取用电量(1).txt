1、获取项目的用电量

/sw/wy/projectEle/used/getByPage
post:
参数
{
  "projectId": "项目id",
  "pageNo": "页码",
  "pageSize": "页大小"
}

响应:
{
	code: 业务响应码 200 500...  
	message: 这个是异常的时候的消息提示
	data:{
		pageNo: '第几页'
		pageSize:'一页多少页'
		totalPages:'总页数'
		totalElements:'总数据量'
		data:[

		{
			id:
		  "date": "2025-07-01",
		  "totalRooms": "总房间数",
		  "rentRooms": "已租房间数",
		  "waitRooms": "待租房间数",
		  "projectId": "项目id",
		  "projectName": "项目名称",
		  "totalUsed": "总用电量",
		  "totalMoney": "总用电金额",
		  "alertNumber": "预警数量",
		  "openKey": "所属物业"
		}
			]
	}
}



2、获取告警数据

/sw/wy/alert/history/findByReportId

post:
参数:
{
  "reportId": "数据行id",
  "pageNo": "页码",
  "pageSize": "页大小"
}

响应:
{
	code: 业务响应码 200 500...  
	message: 这个是异常的时候的消息提示
	data:{
		pageNo: '第几页'
		pageSize:'一页多少页'
		totalPages:'总页数'
		totalElements:'总数据量'
		data:[

	{
  "date": "告警日期",
  "roomId": "房间id",
  "roomNo": "房间号",
  "projectId": "项目id",
  "projectName": "项目名称",
  "reason": "原因"
}
			]
	}
}