<!--
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-05-27 10:49:32
 * @LastEditors: wangxingyue
 * @LastEditTime: 2025-07-21 15:16:45
 * @FilePath: \ban-ban\运营端\admin\src\views\landlord\data\index.vue
-->

<template>
  <div class="app-container">
    <el-table
      v-loading="listLoading"
      :data="list"
      max-height="620"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
	  <el-table-column label="日期" align="center" prop="reportDate">
		<template #default="{ row }">{{ parseTime(row.reportDate).slice(0,10)}}</template>
	  </el-table-column>
      <el-table-column label="房东数" align="center" prop="landlordCount" />
      <el-table-column label="房间数" align="center" prop="roomCount"></el-table-column>
      <el-table-column label="已租" align="center" prop="rentedRoomCount" />
      <el-table-column label="待租" align="center" prop="availableRoomCount" />
      <el-table-column label="收款" align="center" prop="collectionAmount" />
      <el-table-column label="电费" align="center" prop="electricityFeeAmount" />
      <el-table-column label="房租" align="center" prop="rentAmount" />
      <el-table-column label="提现" align="center" prop="withdrawalAmount" />
      <el-table-column label="总收" align="center" prop="totalIncome" />
      <el-table-column label="总提" align="center" prop="totalWithdrawal" />
      <el-table-column label="余额" align="center" prop="balance" />
    </el-table>

    <div class="pagination">
      <el-pagination
        @size-change="fetchData"
        @current-change="fetchData"
        :current-page.sync="querySearch.pageNo"
        :page-sizes="[15, 30, 50, 100]"
        :page-size.sync="querySearch.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="querySearch.total"
      >
      </el-pagination>
    </div>


  </div>
</template>

<script>
import * as api from "@/api/landlord";
export default {
  name: "Landlorddata",
  data() {
    return {
      list: null,
      listLoading: true,
      querySearch: {
        pageNo: 1,
        pageSize: 50,
        total: 0
      }
    };
  },
  created() {
    this.fetchData();
  },
  methods: {
    async fetchData() {
      try {
        this.listLoading = true;
        const { data: res } = await api.getLandlordReport(
          this.querySearch
        );
        const { data, pageNo, pageSize, totalElements } = res;
        this.list = data;
        this.querySearch = {
          pageNo,
          pageSize,
          total: totalElements,
          status: this.querySearch.status,
        };
      } catch (err) {
        console.error(err);
      } finally {
        this.listLoading = false;
      }
    },

 
 
  },
};
</script>

<style scoped lang="scss">
.pagination {
  padding-top: 20px;
  text-align: right;
}
</style>
