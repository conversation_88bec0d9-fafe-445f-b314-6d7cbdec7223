1、商户列表

/sw/sh/shop/getShopList

post
{
	pageNo:1
	pageSize:20,
	status:-2 审核未通过   -1 待审核  0 审核通过   1 已经付费  2 全部
}

响应:
{
  code: 业务响应码 200 500...  
  message: 这个是异常的时候的消息提示
  data:{
    pageNo: '第几页'
    pageSize:'一页多少页'
    totalPages:'总页数'
    totalElements:'总数据量'
    data:[
{
id:id
  "shopName": "店铺名称",
  "mainBusiness": "主营业务",
  "description": "店铺简介",
  "address": "店铺地址",
  "imageUrl": "图片URL列表",
  "contactPerson": "联系人姓名",
  "phone": "联系电话",
  "businessLicenseUrl": "营业执照图片URL",
  "openKey": "商户标识",
  "paymentTime": "付费日",
  "expiredTime": "到期日",
  "totalAmount": "总付费",
  "status": "状态  1 在线付费了  0 审核通过/入驻 -1 待审核  -2 未审核通过",
  "serviceTypeId": "服务id",
  "wellCount": "好评",
  "badCount": "差评",
  "totalCount": "总评论数",
  "typeName": "类型名称",
  "totalRemain": "剩余天数",
  "rejectReason": "拒绝原因"
}]
  }
}

2、批量通过
/sw/sh/shop/passShops

get

ids:1,2,3

响应:
{
	code:200,
	data:null
}


3、审核不通过
/sw/sh/shop/rejectShop
post
{
	id:商户id，
	reason:元婴
}

响应:
{
	code:200,
	data:null
}
