import request from "@/utils/request";

export function uploadFile(data) {
  const formData = new FormData();
  for (const key in data) {
    if (Object.prototype.hasOwnProperty.call(data, key)) {
      const element = data[key];
      formData.append(key, element);
    }
  }
  return request({
    baseURL: "",
    url: "/axy/file/uploadFile",
    method: "post",
    requestType: "form-data",
    data: formData,
  });
}
