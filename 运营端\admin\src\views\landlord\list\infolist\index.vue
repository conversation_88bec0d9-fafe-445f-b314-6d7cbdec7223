<template>
  <div>
    <template v-if="isDetail">
      <!-- 添加router-view用于显示子路由内容 -->
      <router-view />
    </template>
    <template v-else>
      <el-table
        v-loading="listLoading"
        :data="list"
        element-loading-text="Loading"
        border
        fit
        highlight-current-row
      >
        <!-- <el-table-column label="ID" align="center" prop="id" /> -->
        <el-table-column label="公司名称" align="center" prop="companyName" />
        <el-table-column
          label="营业执照"
          align="center"
          prop="businessLicenseUrl"
        >
          <template #default="{ row }">
            <el-image
              style="width: 50px; height: 50px"
              :src="row.businessLicenseUrl"
              fit="contain"
              :preview-src-list="[row.businessLicenseUrl]"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="联系人"
          align="center"
          prop="responsiblePerson"
        />
        <el-table-column
          label="电话"
          align="center"
          prop="responsibleTelephone"
        />
        <el-table-column label="注册时间" align="center" prop="createTime">
          <template #default="{ row }">{{
            parseTime(row.createTime, "YYYY-MM-DD")
          }}</template>
        </el-table-column>
        <el-table-column label="房间数" align="center" prop="totalRooms" />
        <el-table-column label="已租" align="center" prop="rentedRooms" />
        <el-table-column label="待租" align="center" prop="availableRooms" />
        <el-table-column label="收款" align="center" prop="totalIncome" />
        <el-table-column label="提现" align="center" prop="totalWithdrawal" />
        <el-table-column label="余额" align="center" prop="balance" />
        <el-table-column label="操作" align="center">
          <template #default="{ row }">
            <el-button type="text" @click="openCollectionRecord(row)"
              >收款记录</el-button
            >
            <el-button type="text" @click="openWithdrawalRecord(row)"
              >提现记录</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- <div class="pagination">
        <el-pagination
          @size-change="fetchData"
          @current-change="fetchData"
          :current-page.sync="querySearch.pageNo"
          :page-sizes="[15, 30, 50, 100]"
          :page-size.sync="querySearch.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="querySearch.total"
        >
        </el-pagination>
      </div> -->
    </template>
  </div>
</template>

<script>
import * as api from "@/api/landlord";
export default {
  name: "LandlordInfoList",
  data() {
    return {
      list: [],
      listLoading: true,
      // querySearch: {
      //   pageNo: 1,
      //   pageSize: 50,
      //   total: 0,
      // },
    };
  },
  created() {
    this.fetchData();
  },
  computed: {
    isDetail() {
      return this.$route.name !== "LandlordInfoList";
    },
    userId() {
      return this.$route.params.userId || "";
    },
  },
  methods: {
    async fetchData() {
      if (this.userId) {
        try {
          this.listLoading = true;
          const { data } = await api.getOwnerInfo(this.userId);
          this.list = data;

          // this.querySearch = {
          //   pageNo,
          //   pageSize,
          //   total: totalElements,
          //   status: this.querySearch.status,
          // };
        } catch (err) {
          console.error(err);
        } finally {
          this.listLoading = false;
        }
      } else {
        return;
      }
    },
    openCollectionRecord({ openKey }) {
      this.$router.push({
        name: "LandlordcollectionRecord",
        params: {
          openKey: openKey,
        },
      });
    },
    openWithdrawalRecord({ openKey }) {
      this.$router.push({
        name: "LandlordwithdrawalRecord",
        params: {
          openKey: openKey,
        },
      });
    },
  },
};
</script>

<style scoped lang="scss">
.pagination {
  padding-top: 20px;
  text-align: right;
}
</style>