1、上传房间列表
/sw/wy/project/room/importProjectRoom
post:
file:
响应:
{
	code:200,
	message:null,
	data:null
}

2、获取房屋列表

/sw/wy/project/room/getProjectRoom

post:
content-type:application/json
参数:
{
  pageNo: 页码
  pageSize:页大小
}

响应:
{
  code: 业务响应码 200 500...  
  message: 这个是异常的时候的消息提示
  data:{
    pageNo: '第几页'
    pageSize:'一页多少页'
    totalPages:'总页数'
    totalElements:'总数据量'
    data:[
  {
  id:数据id
  "openKey": "物业唯一标识",
  "projectName": "项目名称",
  "buildingNumber": "楼号",
  "roomNumber": "房间号",
  "area": "面积(平方米)",
  "electricityMeterNumber": "电表号",
  "status": "状态(如: 空闲/已租)",
  "price": "报价(元/月)",
  "imageUrl": "房间图片URL",
  "videoUrl": "房间视频URL"
}]
  }
}


2、修改房屋数据

/sw/wy/project/room/modifyProjectRoom

post:
content-type:application/json

参数:
{
  "id": "房屋id",
  "openKey": "物业唯一标识",
  "projectName": "项目名称",
  "buildingNumber": "楼号",
  "roomNumber": "房间号",
  "area": "面积(平方米)",
  "status": "状态(0=已租, 1=空闲)",
  "electricityMeterNumber": "电表号",
  "price": "报价(元/月)",
  "imageUrl": "房间图片URL",
  "videoUrl": "房间视频URL"
}

响应：
{
  code:200,
  message:null,
  data:null
}


