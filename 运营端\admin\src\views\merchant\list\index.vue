<!--
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-05-27 10:49:43
 * @LastEditors: wangxingyue
 * @LastEditTime: 2025-07-25 14:36:01
 * @FilePath: \ban-ban\运营端\admin\src\views\merchant\list\index.vue
-->
<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form inline>
        <el-form-item label="状态：">
          <el-select v-model="querySearch.status" placeholder="请选择状态">
            <el-option
              v-for="(item, index) in statusOptions"
              :key="index"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        &nbsp;
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="fetchData()">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetSearch()"
              >重置</el-button
            >
          <!-- <el-button
            :disabled="!multipleSelection.length"
            type="success"
            @click="handleApply(multipleSelection, 1)"
            >批量通过</el-button
          > -->
          <!-- <el-button
            :disabled="!multipleSelection.length"
            type="warning"
            @click="handleApply(multipleSelection, 0)"
            >批量拒绝</el-button
          > -->
        </el-form-item>
      </el-form>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      max-height="580"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="商户名称" align="center" prop="shopName" />
      <el-table-column label="商户类型" align="center" prop="typeName" />
      <el-table-column
        label="营业执照"
        align="center"
        prop="businessLicenseUrl"
      >
        <template #default="{ row }">
          <el-image
            style="width: 50px; height: 50px"
            :src="row.businessLicenseUrl"
            fit="contain"
            :preview-src-list="[row.businessLicenseUrl]"
          />
        </template>
      </el-table-column>
      <el-table-column label="联系人" align="center" prop="contactPerson" />
      <el-table-column label="手机号" align="center" prop="phone" />
      <el-table-column label="注册时间" align="center" prop="createTime">
        <template #default="{ row }">{{
          parseTime(row.createTime, "YYYY-MM-DD")
        }}</template>
      </el-table-column>
      <el-table-column label="付费时间" align="center" prop="paymentTime">
        <template #default="{ row }">{{
          parseTime(row.paymentTime, "YYYY-MM-DD")
        }}</template>
      </el-table-column>
      <el-table-column label="到期时间" align="center" prop="expiredTime">
        <template #default="{ row }">{{
          parseTime(row.expiredTime, "YYYY-MM-DD")
        }}</template>
      </el-table-column>
      <el-table-column label="总付费额" align="center" prop="totalAmount" />
      <el-table-column label="状态" align="center" prop="status">
        <!-- 状态：-2 审核未通过   -1 待审核  0 审核通过   1 已经付费  2 全部 -->
        <template #default="{ row }">
          <el-tag :type="statusFilter(row.status).type">{{
            statusFilter(row.status).name
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template #default="{ row }">
          <template v-if="row.status == -1">
            <el-button type="text" @click="handleApply([row.id], 1)"
              >通过</el-button
            >
            <el-button type="text" @click="handleReject(row.id)"
              >拒绝</el-button
            >
          </template>
           <template>
            <el-button type="text" @click="handleViewDetail(row)"
              >查看详情</el-button
            >
          </template>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination">
      <el-pagination
        @size-change="fetchData"
        @current-change="fetchData"
        :current-page.sync="querySearch.pageNo"
        :page-sizes="[15, 30, 50, 100]"
        :page-size.sync="querySearch.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="querySearch.total"
      >
      </el-pagination>
    </div>

    <el-dialog
      title="商户详情"
      :visible.sync="detailDialogVisible"
      width="70%"
      top="5vh"
      class="merchant-detail-dialog"
    >
      <div v-if="detailData" class="merchant-detail-container">
        <div class="section-title">基本信息</div>
        <el-descriptions :column="1" border>
          <el-descriptions-item label="商户ID">{{ detailData.id }}</el-descriptions-item>
          <el-descriptions-item label="商户名称">{{ detailData.shopName }}</el-descriptions-item>
          <el-descriptions-item label="商户类型">{{ detailData.typeName }}</el-descriptions-item>
          <el-descriptions-item label="主营业务">
            <div class="long-text-content">{{ detailData.mainBusiness }}</div>
          </el-descriptions-item>
          <!-- <el-descriptions-item label="店铺简介">
            <div class="long-text-content">{{ detailData.description }}</div>
          </el-descriptions-item> -->
          <el-descriptions-item label="店铺地址">
            <div class="long-text-content">{{ detailData.address }}</div>
          </el-descriptions-item>
        </el-descriptions>

        <div class="section-title">详细信息</div>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-card">
              <div class="card-title">联系信息</div>
              <el-descriptions :column="1" border>
                <el-descriptions-item label="联系人">{{ detailData.contactPerson }}</el-descriptions-item>
                <el-descriptions-item label="联系电话">{{ detailData.phone }}</el-descriptions-item>
                <el-descriptions-item label="推荐人电话">{{ detailData.recommenderPhone || '--' }}</el-descriptions-item>
                <el-descriptions-item label="商户标识">{{ detailData.openKey }}</el-descriptions-item>
                <el-descriptions-item label="注册时间">{{ parseTime(detailData.createTime, "YYYY-MM-DD") }}</el-descriptions-item>
                <el-descriptions-item label="付费时间">{{ parseTime(detailData.paymentTime, "YYYY-MM-DD") }}</el-descriptions-item>
                <el-descriptions-item label="到期时间">{{ parseTime(detailData.expiredTime, "YYYY-MM-DD") }}</el-descriptions-item>
              </el-descriptions>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-card">
              <div class="card-title">统计数据</div>
              <el-descriptions :column="1" border>
                <el-descriptions-item label="总付费额">{{ detailData.totalAmount }}</el-descriptions-item>
                <el-descriptions-item label="剩余天数">{{ detailData.totalRemain }}</el-descriptions-item>
                <el-descriptions-item label="好评数">{{ detailData.wellCount }}</el-descriptions-item>
                <el-descriptions-item label="差评数">{{ detailData.badCount }}</el-descriptions-item>
                <el-descriptions-item label="总评论数">{{ detailData.totalCount }}</el-descriptions-item>
                <el-descriptions-item label="状态">
                  <el-tag :type="statusFilter(detailData.status).type">{{ statusFilter(detailData.status).name }}</el-tag>
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </el-col>
        </el-row>

        <el-descriptions v-if="detailData.status === -2" :column="1" border style="margin-top: 20px;">
          <el-descriptions-item label="拒绝原因">
            <div class="long-text-content">{{ detailData.rejectReason }}</div>
          </el-descriptions-item>
        </el-descriptions>

        <div class="section-title">图片资料</div>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="image-container">
              <div class="image-title">营业执照</div>
              <el-image
                class="merchant-image"
                :src="detailData.businessLicenseUrl"
                fit="contain"
                :preview-src-list="[detailData.businessLicenseUrl]"
              />
            </div>
          </el-col>
          <el-col :span="12">
            <div class="image-container">
              <div class="image-title">店铺图片</div>
              <el-image
                v-if="detailData.imageUrl"
                class="merchant-image"
                :src="detailData.imageUrl"
                fit="contain"
                :preview-src-list="detailData.imageUrl ? [detailData.imageUrl] : []"
              />
              <div v-else class="no-image">暂无图片</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as api from "@/api/merchant";
export default {
  name: "Merchantlist",
  data() {
    return {
      list: [],
      listLoading: true,
      querySearch: {
        pageNo: 1,
        pageSize: 50,
        total: 0,
        status: "",
      },

      statusOptions: [
        {
          name: "全部",
          value: 2,
          type: "",
        },
        {
          name: "已上线",
          value: 1,
          type: "success",
        },
        {
          name: "未上线",
          value: 0,
          type: "",
        },
        {
          name: "待审核",
          value: -1,
          type: "warning",
        },
        {
          name: "审核未通过",
          value: -2,
          type: "danger",
        },
      ],

      multipleSelection: [],
      detailDialogVisible: false,
      detailData: null,
    };
  },
  created() {
    this.fetchData();
  },
  methods: {
    statusFilter(status) {
      return (
        this.statusOptions.find((e) => e.value == status) || {
          name: "未知",
          value: "",
          type: "",
        }
      );
    },
    async fetchData() {
      try {
        this.listLoading = true;
        const { data: res } = await api.getShopList(this.querySearch);
        const { data, pageNo, pageSize, totalElements } = res;
        this.list = data;
        this.querySearch = {
          pageNo,
          pageSize,
          total: totalElements,
          status: this.querySearch.status,
        };
      } catch (err) {
        console.error(err);
      } finally {
        this.listLoading = false;
      }
    },
    // 重置搜索条件
    resetSearch() {
      this.querySearch = {
        pageNo: 1,
        pageSize: 50,
        total: 0,
        status: "",
      };
      // 重置到第一页并获取数据
      this.querySearch.pageNo = 1;
      this.fetchData();
    },

     handleSelectionChange(val) {
      console.log(val);
      this.multipleSelection = val.map((e) => e.id);
    },

    async handleApply(ids, type) {
      await this.$confirm(`确定${type === 1 ? "通过" : "拒绝"}吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      });
      try {
        if (type == 1) {
          //通过
          await api.passShops({ ids: ids.join(",") });
        } else {
          // await api.rejectWithdrawal({ ids: ids.join(",") });
        }
        this.$message({
          type: "success",
          message: "操作成功",
        });
        this.multipleSelection = [];
        if (this.$refs.multipleTable) {
          this.$refs.multipleTable.clearSelection();
        }
        // 刷新列表数据
        this.fetchData();
        // 刷新角标数量
        this.$store.dispatch('merchant/fetchWaitShopNum');
      } catch (err) {
        console.error(err);
      }
    },

    async handleReject(id) {
      try {
        const { value: reason } = await this.$prompt('请输入拒绝原因', '拒绝审核', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputValidator: (value) => {
            if (!value) {
              return '拒绝原因不能为空'
            }
            return true
          }
        })

        await api.rejectShop({ id, reason })
        this.$message({
          type: 'success',
          message: '操作成功'
        })
        // 刷新列表数据
        this.fetchData()
        // 刷新角标数量
        this.$store.dispatch('merchant/fetchWaitShopNum')
      } catch (err) {
        console.error(err)
      }
    },

    async handleViewDetail(row) {
      try {
        // 直接使用列表数据
        this.detailData = row;
        this.detailDialogVisible = true;
      } catch (err) {
        console.error(err);
        this.$message.error('获取商户详情失败');
      }
    },
  },
};
</script>

<style scoped lang="scss">
.filter-container {
  padding-bottom: 10px;
  margin-bottom: 15px;
}
.pagination {
  padding-top: 20px;
  text-align: right;
}

.merchant-detail-container {
  padding: 0 10px;
  max-height: 70vh;
  overflow-y: auto;
  scrollbar-width: thin;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: #c0c4cc;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-track {
    background-color: #f5f7fa;
  }
}

.long-text-content {
  max-height: 100px;
  overflow-y: auto;
  word-break: break-all;
  white-space: pre-wrap;
  line-height: 1.5;
  // padding: 8px;
  // background-color: #fafafa;
  border-radius: 4px;
  
  &::-webkit-scrollbar {
    width: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    // background-color: #c0c4cc;
    border-radius: 2px;
  }
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin: 20px 0 12px;
  padding-left: 10px;
  border-left: 4px solid #409EFF;
  line-height: 20px;
}

.info-card {
  margin-bottom: 20px;
  border-radius: 4px;
}

.card-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 10px;
  color: #606266;
}

.image-container {
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 20px;
}

.image-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 10px;
  color: #303133;
}

.merchant-image {
  width: 100%;
  height: 240px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #fff;
}

.no-image {
  width: 100%;
  height: 240px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
  background-color: #fff;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
}

// 自定义弹窗样式
::v-deep .merchant-detail-dialog {
  .el-dialog__body {
    padding: 15px 20px;
    max-height: calc(85vh - 108px);
  }
}
</style>