1、分页获取业主用户信息
/admin/account/findAccountByPage

post: content-type:application/json
{
  "pageNo": 1,
  "pageSize": 10,
  "status": 1,
  "name": "张三"
}

响应:
{
	code: 业务响应码 200 500...  
	message: 这个是异常的时候的消息提示
	data:{
		pageNo: '第几页'
		pageSize:'一页多少页'
		totalPages:'总页数'
		totalElements:'总数据量'
		data:[
		{
		id:id
  "name": "姓名",
  "telephone": "联系电话",
  "account": "账号",
  "password": "密码",
  "description": "描述",
  "openKey": "唯一标识符",
  "status": "0 禁用  1 启用"
}]
	}
}


2、添加业主账号
/admin/account/addAccount

post: content-type:application/json
参数
{
  "name": "姓名",
  "telephone": "联系电话",
  "account": "账号",
  "password": "密码",
  "description": "描述"
}
响应:
{
	code:200,
	data:null
}

3、根据id获取业主账户信息
/admin/account/getInfoById

GET
id

响应：
{
	code:200,
	data:	{
		id:id
  "name": "姓名",
  "telephone": "联系电话",
  "account": "账号",
  "password": "密码",
  "description": "描述",
  "openKey": "唯一标识符",
  "status": "0 禁用  1 启用"
}
}

4、修改用户信息
/admin/account/modifyAccount

post: content-type:application/json

参数:
{		id:id
  "name": "姓名",
  "telephone": "联系电话",
  "password": "密码",
  "description": "描述"
  }

响应:
{
	code:200,
	data:null
}




5、禁用或者启用业主账号
/admin/account/enableOrDisableAccount
GET
id:

响应:
{
	code:200,
	data:null
}


6、删除业主用户
/admin/account/deleteAccount

GET
id

响应:
{
	code:200,
	data:null
}

7、运营端用户密码修改
/admin/auth/modifyPassword
post: content-type:application/json

参数:
{
	id:id
	oldPassword:旧密码
	newPassword:新密码
}

响应:
{
	code:200,
	data:null
}
