/*
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-06-29 00:00:00
 * @LastEditors: wang<PERSON>yue
 * @LastEditTime: 2025-06-29 00:00:00
 * @FilePath: \ban-ban\运营端\admin\src\store\modules\merchant.js
 */
import { getWaitShopNum } from '@/api/merchant'

const state = {
  waitShopNum: 0
}

const mutations = {
  SET_WAIT_SHOP_NUM: (state, num) => {
    state.waitShopNum = num
  }
}

const actions = {
  // 获取待审核商户数量
  async fetchWaitShopNum({ commit }) {
    try {
      const { data } = await getWaitShopNum()
      commit('SET_WAIT_SHOP_NUM', data || 0)
      return data
    } catch (error) {
      console.error('获取待审核商户数量失败:', error)
      commit('SET_WAIT_SHOP_NUM', 0)
      return 0
    }
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
