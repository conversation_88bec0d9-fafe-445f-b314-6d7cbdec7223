<!--
 * @Date: 2022-12-15 15:39:04
 * @LastEditors: wang<PERSON>yue
 * @LastEditTime: 2025-07-15 18:14:31
 * @FilePath: \ban-ban\大业主\admin\src\views\refresh.vue
-->
<template>
  <div class="refresh-container">
    <div class="loading-spinner">
      <i class="el-icon-loading"></i>
      <p>页面刷新中...</p>
    </div>
  </div>
</template>

<script>
/**
 * 这个页面是为了满足刷新功能
 * 当用户点击当前已激活的菜单项时，通过该页面实现组件刷新
 */
export default {
  name: 'Refresh',
  beforeRouteEnter(to, from, next) {
    // 如果有redirect参数，跳转到指定页面，否则返回上一页
    const redirectPath = to.query.redirect;
    if (redirectPath) {
      // 添加短暂延迟，确保组件能够完全卸载和重新挂载
      setTimeout(() => {
        next(vm => {
          vm.$router.replace({
            path: redirectPath,
            query: {
              // 保留原有query参数，但移除timestamp
              ...to.query,
              timestamp: undefined
            }
          });
        });
      }, 10);
    } else {
      next(vm => vm.$router.replace({ ...from }));
    }
  }
}
</script>

<style scoped>
.refresh-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100%;
}

.loading-spinner {
  text-align: center;
}

.loading-spinner i {
  font-size: 32px;
  color: #409EFF;
}

.loading-spinner p {
  margin-top: 10px;
  color: #606266;
}
</style>
