1、提现记录
/sw/admin/wy/info/getAllWithdrawalList
post
content-type:application/json
参数:
{
	pageNo:
	pageSize:
	status: 0 申请状态  1 拒绝  2 已经打款 、、、、、 不传代表查全部

}
响应:
{
	code: 业务响应码 200 500...  
	message: 这个是异常的时候的消息提示
	data:{
		pageNo: '第几页'
		pageSize:'一页多少页'
		totalPages:'总页数'
		totalElements:'总数据量'
		data:[
		{
		id:唯一标识符号
  "totalIncome": "已支付的租金账单总额，payment_status = 1 且 pay_type = 0",
  "totalWithdrawal": "已完成的提现申请总额，status = 2",
  "handleName": "处理人",
  "balance": "计算字段：totalIncome - totalWithdrawal，注意：此字段需要业务逻辑计算，构造函数中不初始化",
  "companyName": "房东名称",
  "openKey": "物业标识符",
  "amount": "提现金额",
  "applyTime": "申请时间",
  "endTime": "结束时间",
  "status": "状态 0 申请  1 拒绝  2 成功"
}]
	}
}


2、已经提现
/sw/admin/wy/info/passWithdrawal

post:
ids: 1,2,3

响应:{
	code:200,
	message:null,
	data:null
}

3、拒绝提现
/sw/admin/wy/info/rejectWithdrawal
post:
ids: 1,2,3

响应:{
	code:200,
	message:null,
	data:null
}