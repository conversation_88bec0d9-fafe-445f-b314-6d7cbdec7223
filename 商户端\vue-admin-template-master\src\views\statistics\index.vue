<!--
 * @Author: CBB
 * @Date: 2025-03-17 23:32:51
 * @LastEditTime: 2025-05-26 18:48:22
 * @LastEditors: wangxingyue
 * @Description:
 * @FilePath: \vue-admin-template-master\src\views\statistics\index.vue
-->
<template>
  <div class="app-container">
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
      <el-table-column label="日期" align="center" prop="reportDate" />
      <el-table-column label="曝光量" align="center" prop="exposureCount" />
      <el-table-column label="收藏数" align="center" prop="favoriteCount" />
      <el-table-column label="电话拨打数" align="center" prop="callCount" />
    </el-table>

    <div class="pagination">
      <el-pagination
        @size-change="fetchData"
        @current-change="fetchData"
        :current-page.sync="querySearch.pageNo"
        :page-sizes="[15, 30, 50, 100]"
        :page-size.sync="querySearch.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="querySearch.total"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { getShDailyReport } from "@/api/table";
export default {
  data() {
    return {
      list: null,
      listLoading: true,
      querySearch: {
        pageNo: 1,
        pageSize: 50,
      },
    };
  },
  created() {
    this.fetchData();
  },
  methods: {
    async fetchData() {
      try {
        this.listLoading = true;
        const { data: res } = await getShDailyReport(this.querySearch);
        console.log(res);
        const { data, pageNo, pageSize, totalElements } = res;
        this.list = data;
        this.querySearch = {
          ...this.querySearch,
          pageNo,
          pageSize,
          total: totalElements,
        };
      } catch (err) {
        console.error(err);
      } finally {
        this.listLoading = false;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.pagination {
  padding-top: 20px;
  text-align: right;
}
</style>
