/*
 *Author: CBB
 *Date: 2025-03-16 21:35:03
 *LastEditTime: 2025-04-01 19:27:17
 *LastEditors: CBB
 *Description:
 *FilePath: \admin\src\store\modules\user.js
 */
import { login, logout, getAccountInfo } from "@/api/user";
import { getToken, setToken, removeToken } from "@/utils/auth";
import { resetRouter } from "@/router";

const getDefaultState = () => {
  return {
    token: getToken(),
    userInfo: {
      openKey: "",
      status: null,
      // step: 0, //0 小区基本资料设置 1 物业基本资料设置 2 小区服务设置 3 缴费积分设置 4 商户号设置 5 物业报修设置 6 物业催费设置 7 所有设置已经就绪(正常)
      step: 0, //0 小区资料 1 物业资料 2 服务设置 3 积分设置 4 商户号设置 5 报修设置 6 催费短信 7 房屋导入 8 所有设置已经就绪(正常)
    },
  };
};

const state = getDefaultState();

const mutations = {
  RESET_STATE: (state) => {
    Object.assign(state, getDefaultState());
  },
  SET_TOKEN: (state, token) => {
    state.token = token;
  },
  SET_USERINFO: (state, userInfo) => {
    state.userInfo = userInfo;
  },
};

const actions = {
  // user login
  login({ dispatch, commit }, loginForm) {
    return new Promise((resolve, reject) => {
      login({ ...loginForm })
        .then((response) => {
          const { data } = response;
          commit("SET_TOKEN", data.token);
          setToken(data.token);
          dispatch("getInfo");
          resolve();
        })
        .catch((error) => {
          reject(error);
        });
    });
  },
  getInfo({ commit, state }) {
    // return new Promise((resolve, reject) => {
    //   getAccountInfo()
    //     .then((response) => {
    //       const { data } = response;
    //       commit("SET_USERINFO", data);
    //       resolve(data);
    //     })
    //     .catch((error) => {
    //       reject(error);
    //     });
    // });
  },

  // user logout
  logout({ commit, state }) {
    return new Promise((resolve, reject) => {
      logout()
        .then(() => {
          removeToken(); // must remove  token  first
          resetRouter();
          commit("RESET_STATE");
          resolve();
        })
        .catch((error) => {
          reject(error);
        });
    });
  },

  // remove token
  resetToken({ commit }) {
    return new Promise((resolve) => {
      removeToken(); // must remove  token  first
      commit("RESET_STATE");
      resolve();
    });
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
