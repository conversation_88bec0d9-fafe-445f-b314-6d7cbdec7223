<template>
  <div class="">
    <!-- 房屋信息展示 -->
    <div style="margin-bottom: 10px">
      <el-descriptions
        title="房屋信息"
        :column="3"
        border
        v-loading="roomInfoLoading"
      >
        <el-descriptions-item label="项目名称">{{
          roomInfo.projectName
        }}</el-descriptions-item>
        <el-descriptions-item label="楼号">{{
          roomInfo.buildingNumber
        }}</el-descriptions-item>
        <el-descriptions-item label="房间号">{{
          roomInfo.roomNumber
        }}</el-descriptions-item>
        <!-- <el-descriptions-item label="面积(平方米)">{{
          roomInfo.area
        }}</el-descriptions-item>
        <el-descriptions-item label="电表号">{{
          roomInfo.electricityMeterNumber
        }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="roomInfo.status == '0' ? 'success' : 'info'">
            {{ roomInfo.status == '0' ? '已租' : '待租' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="报价(元/月)">{{
          roomInfo.price
        }}</el-descriptions-item>
        <el-descriptions-item label="房间图片">
          <el-image 
            v-if="roomInfo.imageUrl" 
            :src="roomInfo.imageUrl" 
            style="width: 100px; height: 100px;"
            :preview-src-list="[roomInfo.imageUrl]">
          </el-image>
          <span v-else>暂无图片</span>
        </el-descriptions-item>
        <el-descriptions-item label="房间视频">
          <video 
            v-if="roomInfo.videoUrl" 
            :src="roomInfo.videoUrl" 
            controls 
            style="width: 200px; max-height: 150px;">
            您的浏览器不支持视频标签
          </video>
          <span v-else>暂无视频</span>
        </el-descriptions-item> -->
      </el-descriptions>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      max-height="580"
      style="width: 100%"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
      <el-table-column label="租赁状态" align="center">
        <template #default="{ row }">
          <el-tag :type="statusFilter(row.leaseStatus).type">
            {{ statusFilter(row.leaseStatus).name }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="起租日期" align="center" prop="startDate">
        <template #default="{ row }">
          {{ parseTime(row.startDate, "YYYY-MM-DD") }}
        </template>
      </el-table-column>
      <el-table-column label="到期日期" align="center" prop="endDate">
        <template #default="{ row }">
          {{ parseTime(row.endDate, "YYYY-MM-DD") }}
        </template>
      </el-table-column>
      <el-table-column label="月租金(元)" align="center" prop="monthlyRent" />
      <el-table-column label="承租人姓名" align="center" prop="tenantName" />
      <el-table-column label="联系电话" align="center" prop="phone" />
      <el-table-column label="所属行业" align="center" prop="industry" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="客户类型" align="center" v-if="false">
        <template #default="{ row }">
          {{ row.status === "0" ? "当前客户" : "结束租赁客户" }}
        </template>
      </el-table-column>
      <el-table-column
        label="押金(元)"
        align="center"
        prop="deposit"
        v-if="false"
      />
      <el-table-column label="付款方式" align="center" v-if="false">
        <template #default="{ row }">
          {{ getPaymentTypeText(row.paymentType) }}
        </template>
      </el-table-column>
      <el-table-column
        label="总租金"
        align="center"
        prop="totalAmount"
        v-if="false"
      />
      <el-table-column
        label="已付租金"
        align="center"
        prop="payedAmount"
        v-if="false"
      />
    </el-table>

    <div class="pagination">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="queryParams.pageNo"
        :page-sizes="[10, 20, 30, 50]"
        :page-size.sync="queryParams.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
  </div>
</template>
  
  <script>
import { getLeaseHistory,getProjectRoomInfo  } from "@/api/room";

export default {
  data() {
    return {
      list: [],
      listLoading: true,
      total: 0,
      queryParams: {
        pageNo: 1,
        pageSize: 50,
        roomId: "",
      },

       roomInfo: {
        id: "",
        openKey: "",
        projectName: "",
        buildingNumber: "",
        roomNumber: "",
        area: "",
        electricityMeterNumber: "",
        status: "",
        price: "",
        imageUrl: "",
        videoUrl: "",
      },
      roomInfoLoading: true,
    };
  },
  computed: {
    roomId() {
      return this.$route.params.id || "";
    },
  },
  created() {
    this.queryParams.roomId = this.roomId;
    this.fetchData();
        this.fetchRoomInfo();

  },
  methods: {
    statusFilter(status) {
      const statusMap = {
        0: { name: "已租", type: "success" },
        1: { name: "欠费", type: "danger" },
        2: { name: "已到期", type: "warning" },
      };
      return statusMap[status] || { name: "未知状态", type: "info" };
    },
    getPaymentTypeText(type) {
      const typeMap = {
        0: "年付",
        1: "半年付",
        2: "季付",
        3: "月付",
        4: "日付",
      };
      return typeMap[type] || "未知方式";
    },
    async fetchData() {
      try {
        this.listLoading = true;
        const res = await getLeaseHistory(this.queryParams);
        if (res.code === 200) {
          this.list = res.data.data || [];
          this.total = res.data.totalElements || 0;
        } else {
          this.$message.error(res.message || "获取租赁历史失败");
          this.list = [];
          this.total = 0;
        }
      } catch (err) {
        console.error(err);
        this.$message.error("获取租赁历史列表失败");
        this.list = [];
        this.total = 0;
      } finally {
        this.listLoading = false;
      }
    },
    handleSizeChange(val) {
      this.queryParams.pageSize = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.queryParams.pageNo = val;
      this.fetchData();
    },

    async fetchRoomInfo() {
      try {
        this.roomInfoLoading = true;
        const res = await getProjectRoomInfo(this.roomId);
        if (res.code === 200) {
          this.roomInfo = res.data || {};
        } else {
          this.$message.error(res.message || "获取房屋信息失败");
          this.roomInfo = {};
        }
      } catch (err) {
        console.error(err);
        this.$message.error("获取房屋信息失败");
        this.roomInfo = {};
      } finally {
        this.roomInfoLoading = false;
      }
    },
  },
};
</script>
  
  <style scoped lang="scss">
.pagination {
  padding-top: 20px;
  text-align: right;
}

::v-deep .el-descriptions__header {
  margin-bottom: 10px !important;
}
</style>