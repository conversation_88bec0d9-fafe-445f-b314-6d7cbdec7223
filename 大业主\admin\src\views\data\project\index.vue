<template>
  <div class="app-container">
    <div class="btnBox">
      <div>
        <el-button type="primary" icon="el-icon-plus" @click="openActivity()">创建项目</el-button>
      </div>
      <div>
        <el-button size="medium" type="primary" icon="el-icon-download" @click="downloadTemplate()"
          >下载模版</el-button
        >
      </div>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      max-height="580"
      style="width: 100%"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
      <!-- <el-table-column
        label="项目名称"
        align="center"
        prop="projectName"
        column-key="projectName"
        :filters="projectFilters"
        :filter-method="filterProjectName"
      /> -->
      <el-table-column
        label="项目名称"
        align="center"
        prop="projectName"
        column-key="projectName"
      />
      <el-table-column label="地址" align="center" prop="address" />
      <el-table-column label="联系人" align="center" prop="linkPerson" />
      <el-table-column label="联系电话" align="center" prop="linkPhone" />
      <!-- <el-table-column label="所属物业" align="center" prop="openKey" /> -->
      <el-table-column label="创建时间" align="center" prop="createTime">
        <template #default="{ row }">{{
          parseTime(row.createTime, "YYYY-MM-DD")
        }}</template>
      </el-table-column>
      <el-table-column label="房间数" align="center" prop="totalRooms" />
      <el-table-column label="待租房间数" align="center" prop="waitingRooms" />
      <el-table-column label="已租房间数" align="center" prop="rentRooms" />
      <el-table-column label="操作" align="center">
        <template #default="{ row }">
          <el-button type="text" @click="openActivity(row)">修改</el-button>
          <el-upload
            class="upload-inline"
            :action="`${BASE_URL}/sw/wy/project/room/importProjectRoom`"
            :on-success="(res) => handleSuccess(res, row)"
            :limit="1"
            :before-upload="handleUpload"
            accept=".xlsx"
            with-credentials
            :multiple="false"
            :show-file-list="false"
            name="file"
            :data="{ projectId: row.id }"
            :headers="{
              token,
            }"
          >
            <el-button type="text">上传房间</el-button>
          </el-upload>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination">
      <el-pagination
        @size-change="fetchData"
        @current-change="fetchData"
        :current-page.sync="querySearch.pageNo"
        :page-sizes="[15, 30, 50, 100]"
        :page-size.sync="querySearch.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="querySearch.total"
      >
      </el-pagination>
    </div>

    <!-- 创建/修改项目抽屉 -->
  <el-drawer
    :title="drawerType === 'add' ? '创建项目' : '修改项目'"
    :visible.sync="drawerVisible"
    direction="rtl"
    size="500px"
    :before-close="handleClose"
  >
    <div class="drawer-container">
      <el-form
        ref="projectForm"
        :model="projectForm"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model="projectForm.projectName" placeholder="请输入项目名称" :disabled="drawerType !== 'add'"></el-input>
        </el-form-item>
        <el-form-item label="地址" prop="address">
          <el-input v-model="projectForm.address" placeholder="请输入地址" :disabled="drawerType !== 'add'"></el-input>
        </el-form-item>
        <el-form-item label="联系人" prop="linkPerson">
          <el-input v-model="projectForm.linkPerson" placeholder="请输入联系人"></el-input>
        </el-form-item>
        <el-form-item label="联系电话" prop="linkPhone">
          <el-input v-model="projectForm.linkPhone" placeholder="请输入联系电话"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitForm">提交</el-button>
          <el-button @click="drawerVisible = false">取消</el-button>
        </el-form-item>
      </el-form>
    </div>
  </el-drawer>
  </div>
</template>

<script>
import * as api from "@/api/project";
import { getToken } from "@/utils/auth";

export default {
  name: "Electricity",
  data() {
    return {
      BASE_URL: process.env.VUE_APP_SERVER_URL,
      list: null,
      listLoading: true,
      projects: [],
    //   projectFilters: [],
      querySearch: {
        pageNo: 1,
        pageSize: 50,
        total: 0,
      },
      // 抽屉相关数据
      drawerVisible: false,
      drawerType: 'add', // add-创建 edit-修改
      projectForm: {
        id: '',
        projectName: '',
        address: '',
        linkPerson: '',
        linkPhone: '',
      },
      rules: {
        projectName: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
        address: [{ required: true, message: '请输入地址', trigger: 'blur' }],
        linkPerson: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
        linkPhone: [
          { required: true, message: '请输入联系电话', trigger: 'blur' },
          { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
      }
    };
  },
  created() {
    // this.getProjects();
    this.fetchData();
  },
  computed: {
    token() {
      return getToken();
    },
  },
  methods: {
    // // 获取所有项目
    // async getProjects() {
    //   try {
    //     const { data } = await api.getAllProject();
    //     this.projects = data || [];
    //     // 构建表格筛选项
    //     this.projectFilters = this.projects.map((item) => ({
    //       text: item.projectName,
    //       value: item.id,
    //     }));
    //   } catch (err) {
    //     console.error(err);
    //   }
    // },
    // // 项目名称筛选方法
    // filterProjectName(value, row) {
    //   return row.projectId === value;
    // },
    async fetchData() {
      try {
        this.listLoading = true;
        const { data: res } = await api.getProjectPage(this.querySearch);
        const { data, pageNo, pageSize, totalElements } = res;
        this.list = data;
        this.querySearch = {
          ...this.querySearch,
          pageNo,
          pageSize,
          total: totalElements,
        };
      } catch (err) {
        console.error(err);
      } finally {
        this.listLoading = false;
      }
    },

    handleSuccess(response, row) {
      console.log("handleSuccess==>", response, row);
      const { code, message } = response;
      if (code == 200) {
        this.$message.success("导入成功！");
        setTimeout(() => {
          this.fetchData();
        }, 500);
      } else {
        this.$message.error(message || "导入失败");
      }
    },
    handleUpload(file) {
      console.log("handleUpload==>", file);
      const is_xlsx = file.name.indexOf(".xlsx") !== -1;
      if (!is_xlsx) {
        return this.$message.error("上传文件只能是 xlsx格式 !");
      }
    },
    
    // 打开创建/修改项目抽屉
    openActivity(row) {
      if (row) {
        // 修改项目
        this.drawerType = 'edit';
        this.projectForm = {
          id: row.id,
          projectName: row.projectName,
          address: row.address,
          linkPerson: row.linkPerson,
          linkPhone: row.linkPhone
        };
      } else {
        // 创建项目
        this.drawerType = 'add';
        this.projectForm = {
          projectName: '',
          address: '',
          linkPerson: '',
          linkPhone: '',
        };
      }
      this.drawerVisible = true;
    },
    
    // 关闭抽屉
    handleClose() {
      this.$refs.projectForm.resetFields();
      this.drawerVisible = false;
    },
    
    // 提交表单
    submitForm() {
      this.$refs.projectForm.validate(async (valid) => {
        if (valid) {
          try {
            this.listLoading = true;
            if (this.drawerType === 'add') {
              // 创建项目
              const { code, message } = await api.addProject(this.projectForm);
              if (code === 200) {
                this.$message.success('创建项目成功');
                this.drawerVisible = false;
                this.fetchData();
              } else {
                this.$message.error(message || '创建项目失败');
              }
            } else {
              // 修改项目
              const { code, message } = await api.modifyProject(this.projectForm);
              if (code === 200) {
                this.$message.success('修改项目成功');
                this.drawerVisible = false;
                this.fetchData();
              } else {
                this.$message.error(message || '修改项目失败');
              }
            }
          } catch (error) {
            console.error(error);
            this.$message.error('操作失败');
          } finally {
            this.listLoading = false;
          }
        }
      });
    },
    
    // 下载模板
    downloadTemplate() {
      window.open("/xlsx/房间信息模板.xlsx", "_blank");
    },
  },
};
</script>

<style scoped lang="scss">
.filter-container {
  padding-bottom: 15px;
}
.pagination {
  padding-top: 20px;
  text-align: right;
}
.btnBox {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
.drawer-container {
  padding: 20px;
}
.upload-inline {
  display: inline-block;
  margin-left: 10px;
}
</style>