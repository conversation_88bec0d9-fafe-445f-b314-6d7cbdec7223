/*
 *Author: CBB
 *Date: 2025-03-16 21:35:03
 *LastEditTime: 2025-04-01 20:30:18
 *LastEditors: CBB
 *Description:
 *FilePath: \admin\src\permission.js
 */
import router from "./router";
import store from "./store";
import { Notification } from "element-ui";
import NProgress from "nprogress"; // progress bar
import "nprogress/nprogress.css"; // progress bar style
import { getToken } from "@/utils/auth"; // get token from cookie
import getPageTitle from "@/utils/get-page-title";

NProgress.configure({ showSpinner: false }); // NProgress Configuration

const whiteList = ["/login", "/404"]; // no redirect whitelist

router.beforeEach(async (to, from, next) => {
  console.log("to==>", to);
  console.log("from==>", from);

  // start progress bar
  NProgress.start();

  // set page title
  document.title = getPageTitle(to.meta.title);

  // determine whether the user has logged in
  const hasToken = getToken();

  if (hasToken) {
    if (to.path === "/login") {
      // if is logged in, redirect to the home page
      next({ path: "/" });
      NProgress.done();
    } else {
      await store.dispatch("user/getInfo");
      // // 0 小区基本资料设置 1 物业基本资料设置 2 小区服务设置 3 缴费积分设置 4 商户号设置 5 物业报修设置 6 物业催费设置 7 所有设置已经就绪(正常)
      //0 小区资料 1 物业资料 2 服务设置 3 积分设置 4 商户号设置 5 报修设置 6 催费短信 7 房屋导入 8 所有设置已经就绪(正常)
      const { stepPage, userInfo } = store.getters;
      if (stepPage?.name && to.name !== stepPage.name) {
        Notification.closeAll();
        // setTimeout(() => {
        //   Notification({
        //     title: "必填设置页面",
        //     message: `步骤${userInfo.step + 1}（${stepPage.title}）`,
        //     center: true,
        //     duration: 10 * 1000,
        //     showClose: true,
        //     type: "warning",
        //   });
        // }, 0);
        // 注释掉强制跳转的逻辑，允许自由导航
        // next({ name: stepPage.name });
        // NProgress.done();
        next();
      } else {
        next();
      }
    }
  } else {
    /* has no token*/
    if (whiteList.indexOf(to.path) !== -1) {
      // in the free login whitelist, go directly
      next();
    } else {
      // other pages that do not have permission to access are redirected to the login page.
      next(`/login?redirect=${to.path}`);
      NProgress.done();
    }
  }
});

router.afterEach(() => {
  // finish progress bar
  console.log(111);

  NProgress.done();
});
