<!--
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-06-10 15:21:28
 * @LastEditors: wangxingyue
 * @LastEditTime: 2025-07-21 15:23:51
 * @FilePath: \ban-ban\运营端\admin\src\views\systemSet\notice\index.vue
-->
<template>
  <div class="app-container">
    <div class="addBtn">
      <el-button type="primary" @click="openActivity()">发布通知</el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      max-height="580"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
      <el-table-column label="序号" type="index" width="50" align="center" />
      <el-table-column label="标题" align="center" prop="title" />
      <el-table-column label="内容" align="center" prop="content" />
      <el-table-column label="发布时间" align="center" prop="publishTime">
        <template #default="{ row }">
          {{ parseTime(row.publishTime, "YYYY-MM-DD") }}
        </template>
      </el-table-column>
      <el-table-column label="发布人" align="center" prop="publisher" />

      <el-table-column label="操作" align="center">
        <template #default="{ row }">
          <el-button type="text" @click="openActivity(row)">修改</el-button>
          <el-button type="text" @click="delRow(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination">
      <el-pagination
        @size-change="fetchData"
        @current-change="fetchData"
        :current-page.sync="querySearch.pageNo"
        :page-sizes="[15, 30, 50, 100]"
        :page-size.sync="querySearch.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="querySearch.total"
      >
      </el-pagination>
    </div>

    <el-drawer
      :title="form.id ? '修改通知' : '发布通知'"
      :visible.sync="drawerShow"
      direction="rtl"
      size="800px"
    >
      <div class="drawer">
        <el-form
          ref="form"
          :model="form"
          label-width="100px"
          size="small"
          :rules="rules"
          :disabled="submitLoading"
        >
          <el-form-item label="标题：" prop="title">
            <el-input
              v-model="form.title"
              placeholder="请输入活动标题"
            ></el-input>
          </el-form-item>

          <el-form-item label="内容：" prop="content">
            <el-input
              v-model="form.content"
              type="textarea"
              placeholder="请输入内容"
              autosize
            ></el-input>
          </el-form-item>

          <el-form-item label="发布人：" prop="publisher">
            <el-input
              v-model="form.publisher"
              placeholder="请输入发布人"
            ></el-input>
          </el-form-item>
        </el-form>
        <div class="btnBox">
          <el-button type="primary" @click="onSubmit()" :loading="submitLoading"
            >提交</el-button
          >
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import * as api from "@/api/system";
export default {
  data() {
    return {
      list: null,
      listLoading: true,
      querySearch: {
        pageNo: 1,
        pageSize: 50,
        total: 0,
      },
      form: {
        title: "",
        content: "",
        publisher: ""
      },
      rules: {
        title: [{ required: true, message: "请输入标题", trigger: "blur" }],
        content: [{ required: true, message: "请输入内容", trigger: "blur" }],
        publisher: [{ required: true, message: "请输入发布人", trigger: "blur" }],

      },
      submitLoading: false,
      drawerShow: false,
    };
  },
  created() {
    this.fetchData();
  },
  methods: {
    async fetchData() {
      try {
        this.listLoading = true;
        const { data: res } = await api.getAdviceByPage(this.querySearch);
        console.log(res);
        const { data, pageNo, pageSize, totalElements } = res;
        this.list = data;
        this.querySearch = {
          ...this.querySearch,
          pageNo,
          pageSize,
          total: totalElements,
        };
      } catch (err) {
        console.error(err);
      } finally {
        this.listLoading = false;
      }
    },

    async openActivity(row = {}) {
      if (row.id) {
        //编辑
        this.form = {
          ...row,
        };
      } else {
        //新增
        this.form = {
          title: "",
          content: "",
          publisher: ""
        };
      }
      this.drawerShow = true;
    },
    async onSubmit() {
      await this.$refs.form.validate();
      const postData = {
        ...this.form,
      };
      console.log(postData);

      try {
        this.submitLoading = true;
        if (postData.id) {
          //编辑
          await api.modifyAdvice(postData);
        } else {
          //新增
          await api.addAdvice(postData);
        }
        this.$message({
          type: "success",
          message: "提交成功",
        });
        this.drawerShow = false;
        this.fetchData();
      } catch (err) {
        console.error(err);
      } finally {
        this.submitLoading = false;
      }
    },
    async delRow(row) {
      await this.$confirm(`确认删除【${row.title}】通知吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      });
      await api.removeAdvice({ id: row.id });
      this.$message({
        type: "success",
        message: "操作成功",
      });
      this.fetchData();
    },
  },
};
</script>

<style scoped lang="scss">
.pagination {
  padding-top: 20px;
  text-align: right;
}
.drawer {
  padding: 0 20px;
}
.btnBox {
  padding-left: 150px;
}
.addBtn {
  padding-bottom: 20px;
  padding-left: 10px;
}
::v-deep .hide .el-upload--picture-card {
  display: none;
}
</style>