<template>
  <div class="app-container">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="免审核提现额度" name="0"></el-tab-pane>
      <el-tab-pane label="商品返积分比例" name="1"></el-tab-pane>
      <el-tab-pane label="订单返积分天数" name="2"></el-tab-pane>
      <el-tab-pane label="物业分成比例" name="3"></el-tab-pane>
      <el-tab-pane label="默认头像配置" name="4"></el-tab-pane>
      <el-tab-pane label="首次登录积分" name="5"></el-tab-pane>
      <el-tab-pane label="支付超时时间" name="6"></el-tab-pane>
      <el-tab-pane label="小程序系统配置" name="7"></el-tab-pane>
      <el-tab-pane label="商户月金额配置" name="8"></el-tab-pane>

    </el-tabs>

    <div class="center" style="width: 350px">
      <el-form ref="form" :model="form" label-width="120px">
        <el-form-item :label="labelName" v-if="activeName != 4">
          <el-input v-model="limit" placeholder="配置键值" :type="activeName == 7 ? 'textarea' : 'text'"></el-input>
        </el-form-item>
        <el-form-item label="默认头像配置" v-if="activeName == 4">
          <el-upload
            class="avatar-uploader"
            :action="`${BASE_URL}/axy/file/uploadFile`"
            :show-file-list="false"
            :on-success="handleSuccess"
            :before-upload="handleUpload"
            :on-remove="() => (imageUrl = '')"
          >
            <img
              v-if="imageUrl"
              :src="imageUrl"
              style="width: 178px; height: 178px"
            />
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>
      </el-form>

      <div style="width: 350px; margin: 0 auto" v-if="activeName != 4">
        <el-button type="primary" @click="onSubmit" style="margin-left: 100px"
          >确定</el-button
        >
      </div>
    </div>
  </div>
</template>

<script>
import {
  getWithdrawal,
  getGoodsUserPercent,
  getGoodsEndTime,
  getWyPlatformDividend,
  getDefaultHeadUrl,
  getFirstBindPhonePoints,
  getOrderPayTimeout,
  setSystemConfig,
  getPageConfig,
  getShPayMoney
} from "@/api/system.js";
export default {
  data() {
    return {
      BASE_URL: process.env.VUE_APP_SERVER_URL,
      fileList: [],
      activeName: "0",
      limit: 0,
      form: {
        id: "",
        name: "",
      },
      mainId: "",
      labelName: "提现额度",
      imageUrl: "",
    };
  },
  mounted() {
    this.getList();
  },
  activated() {},
  methods: {
    handleClick(e) {
      this.limit = "";
      if (e.index == 0) {
        this.labelName = "提现额度";
        this.getList();
      } else if (e.index == 1) {
        this.labelName = "商品返积分比例";
        this.getList2();
      } else if (e.index == 2) {
        this.labelName = "订单返积分天数";
        this.getList3();
      } else if (e.index == 3) {
        this.labelName = "物业分成比例";
        this.getList4();
      } else if (e.index == 4) {
        this.labelName = "默认头像配置";
        this.getList5();
      } else if (e.index == 5) {
        this.labelName = "首次登录积分";
        this.getList6();
      } else if (e.index == 6) {
        this.labelName = "支付超时时间";
        this.getList7();
      } else if (e.index == 7) {
        this.labelName = "小程序系统配置";
        this.getList8();
      } else if (e.index == 8) {
        this.labelName = "商户月金额配置";
        this.getList9();
      }
    },
    getList() {
      const that = this;
      var obj = {};
      getWithdrawal(obj).then((res) => {
        console.log("res", res);
        if (res.code == 200) {
          that.mainId = res.data.id;
          that.limit = res.data.template;
        } else {
          this.$message(res.message);
        }
      });
    },
    getList2() {
      const that = this;
      var obj = {};
      getGoodsUserPercent(obj).then((res) => {
        console.log("res", res);
        if (res.code == 200) {
          that.mainId = res.data.id;
          that.limit = res.data.template;
        } else {
          this.$message(res.message);
        }
      });
    },
    getList3() {
      const that = this;
      var obj = {};
      getGoodsEndTime(obj).then((res) => {
        console.log("res", res);
        if (res.code == 200) {
          that.mainId = res.data.id;
          that.limit = res.data.template;
        } else {
          this.$message(res.message);
        }
      });
    },
    getList4() {
      const that = this;
      var obj = {};
      getWyPlatformDividend(obj).then((res) => {
        console.log("res", res);
        if (res.code == 200) {
          that.mainId = res.data.id;
          that.limit = res.data.template;
        } else {
          this.$message(res.message);
        }
      });
    },
    getList5() {
      const that = this;
      var obj = {};
      getDefaultHeadUrl(obj).then((res) => {
        console.log("res", res);
        if (res.code == 200) {
          that.mainId = res.data.id;
          that.imageUrl = res.data.template;

          // this.fileList = [{url: res.data.template},];
        } else {
          this.$message(res.message);
        }
      });
    },
    getList6() {
      const that = this;
      var obj = {};
      getFirstBindPhonePoints(obj).then((res) => {
        console.log("res", res);
        if (res.code == 200) {
          that.mainId = res.data.id;
          that.limit = res.data.template;
        } else {
          this.$message(res.message);
        }
      });
    },
    getList7() {
      const that = this;
      var obj = {};
      getOrderPayTimeout(obj).then((res) => {
        console.log("res", res);
        if (res.code == 200) {
          that.mainId = res.data.id;
          that.limit = res.data.template;
        } else {
          this.$message(res.message);
        }
      });
    },
    getList8() {
      const that = this;
      var obj = {};
      getPageConfig(obj).then((res) => {
        console.log("res", res);
        if (res.code == 200) {
          that.mainId = res.data.id;
          that.limit = res.data.template;
        } else {
          this.$message(res.message);
        }
      });
    },
    getList9() {
      const that = this;
      var obj = {};
      getShPayMoney(obj).then((res) => {
        console.log("res", res);
        if (res.code == 200) {
          that.mainId = res.data.id;
          that.limit = res.data.template;
        } else {
          this.$message(res.message);
        }
      });
    },
    handleSuccess(response, file, fileList) {
      console.log("handleSuccess==>", response, file, fileList);
      const { code, data, message } = response;
      if (code == 200) {
        this.imageUrl = data;
        this.onSubmit();
      } else {
        this.$message.error(message || "上传失败");
      }
    },
    handleUpload(file) {
      console.log("handleUpload==>", file);
      const isJPG = ["image/jpg", "image/png", "image/jpeg"].includes(
        file.type
      );
      const isLt2M = file.size / 1024 / 1024 < 10;
      console.log("isLt2M", isLt2M);
      if (!isJPG) {
        return this.$message.error("上传图片只能是 JPG/PNG/JPEG 格式!");
      }
      if (!isLt2M) {
        alert();
        return this.$message.error("上传图片大小不能超过 10MB!");
      }
    },

    onSubmit() {
      const that = this;
      if (that.activeName == 4) {
        that.limit = that.imageUrl;
      }
      var obj = {
        id: that.mainId,
        template: that.limit,
      };
      console.log("obj", obj);
      setSystemConfig(obj).then((res) => {
        console.log("res", res);
        if (res.code == 200) {
          this.$message({
            message: "保存成功",
            type: "success",
          });
          if (that.activeName == 0) {
            this.getList();
          } else if (that.activeName == 1) {
            this.getList2();
          } else if (that.activeName == 2) {
            this.getList3();
          } else if (that.activeName == 3) {
            this.getList4();
          } else if (that.activeName == 4) {
            this.getList5();
          } else if (that.activeName == 5) {
            this.getList6();
          } else if (that.activeName == 6) {
            this.getList7();
          } else if (that.activeName == 7) {
            this.getList8();
          }
        } else {
          this.$message({
            message: res.message,
            type: "error",
          });
        }
      });
    },
  },
};
</script>

<style lang="scss">
::v-deep .el-upload {
  border: 1px border #9999 !important;
}
</style>
<style scoped lang="scss">
.pagination {
  padding-top: 20px;
  text-align: right;
}

.drawer {
  padding: 0 20px;
}

.btns {
  margin-bottom: 20px;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

::v-deep .el-upload {
  width: 178px;
  height: 178px;
  border: 1px dashed #d9d9d9;
}
</style>
