<!--
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-05-27 10:49:41
 * @LastEditors: wangxingyue
 * @LastEditTime: 2025-06-09 10:35:45
 * @FilePath: \ban-ban\运营端\admin\src\views\merchant\data\index.vue
-->

<template>
  <div class="app-container">
    <el-table
      v-loading="listLoading"
      :data="list"
      max-height="620"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
	  <el-table-column label="日期" align="center" prop="reportDate">
		<template #default="{ row }">{{ parseTime(row.reportDate).slice(0,10)}}</template>
	  </el-table-column>
      <el-table-column label="商户数" align="center" prop="merchantCount" />
      <el-table-column label="新增" align="center" prop="newMerchantCount"></el-table-column>
      <el-table-column label="付费数" align="center" prop="paidMerchantCount" />
      <el-table-column label="付费额" align="center" prop="paymentAmount" />
    </el-table>

    <div class="pagination">
      <el-pagination
        @size-change="fetchData"
        @current-change="fetchData"
        :current-page.sync="querySearch.pageNo"
        :page-sizes="[15, 30, 50, 100]"
        :page-size.sync="querySearch.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="querySearch.total"
      >
      </el-pagination>
    </div>


  </div>
</template>

<script>
import * as api from "@/api/merchant";
export default {
  name: "Merchantdata",
  data() {
    return {
      list: null,
      listLoading: true,
      querySearch: {
        pageNo: 1,
        pageSize: 50,
        total: 0
      }
    };
  },
  created() {
    this.fetchData();
  },
  methods: {
    async fetchData() {
      try {
        this.listLoading = true;
        const { data: res } = await api.getMerchantReport(
          this.querySearch
        );
        const { data, pageNo, pageSize, totalElements } = res;
        this.list = data;
        this.querySearch = {
          pageNo,
          pageSize,
          total: totalElements,
          status: this.querySearch.status,
        };
      } catch (err) {
        console.error(err);
      } finally {
        this.listLoading = false;
      }
    },

 
 
  },
};
</script>

<style scoped lang="scss">
.pagination {
  padding-top: 20px;
  text-align: right;
}
</style>
