<template>
  <div class="wrap" v-loading="pageLoading">
    <div class="form">
      <el-form
        ref="form"
        :model="form"
        label-width="150px"
        size="small"
        :rules="rules"
        :disabled="!isEdit || submitLoading"
      >
        <!-- <div class="formTitle">账户信息</div>
        <el-form-item label="商户ID">
          <p>{{ form.openKey ? form.openKey : "--" }}</p>
        </el-form-item>
        <el-form-item label="状态">
          <el-tag :type="filterStatus.type">{{ filterStatus.text }}</el-tag>
        </el-form-item> -->

        <div class="formTitle">商户资料</div>
        <el-form-item label="商户ID：" v-if="form.openKey">
          <el-tag size="mini" effect="plain">{{ form.openKey ? form.openKey : "--" }}</el-tag>
        </el-form-item>
        <el-form-item label="店铺名称：" prop="shopName">
          <el-input v-model="form.shopName" placeholder="请输入店铺名称"></el-input>
        </el-form-item>
        <el-form-item label="服务类型：" prop="serviceTypeId">
          <el-input v-model="form.serviceTypeId" placeholder="请输入服务类型"></el-input>
        </el-form-item>
        <el-form-item label="主营业务：" prop="mainBusiness">
          <el-input
            v-model="form.mainBusiness"
            placeholder="请输入主营业务"
            type="textarea"
          ></el-input>
        </el-form-item>
        <el-form-item label="店铺简介：" prop="description">
          <el-input
            v-model="form.description"
            placeholder="请输入店铺简介"
            type="textarea"
          ></el-input>
        </el-form-item>
        <el-form-item label="店铺地址：" prop="address">
          <el-input
            v-model="form.address"
            type="textarea"
            placeholder="请输入店铺地址"
            autosize
          ></el-input>
        </el-form-item>
        <el-form-item label="门店照片：" prop="imageUrl">
          <el-upload
            :class="{ hide: hideUpload1 }"
            class="avatar-uploader"
            :action="`${BASE_URL}/axy/file/uploadFile`"
            :on-success="handleSuccess1"
            :limit="1"
            :before-upload="handleUpload"
            accept="image/jpeg,image/png,image/jpg"
            with-credentials
            :multiple="false"
            :file-list="fileList1"
            list-type="picture-card"
            :on-remove="() => (form.imageUrl = '')"
          >
            <i class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
          <span v-show="!hideUpload1" class="uploadTips">
            请上传门店照片，仅支持jpg、png、jpeg格式的图片，且单张大小不能超过10M</span
          >
        </el-form-item>
        <el-form-item label="联系人：" prop="contactPerson">
          <el-input
            v-model="form.contactPerson"
            placeholder="请输入联系人"
          ></el-input>
        </el-form-item>
        <el-form-item label="联系电话：" prop="phone">
          <el-input
            v-model="form.phone"
            placeholder="请输入联系电话"
          ></el-input>
        </el-form-item>

        <el-form-item label="营业执照：" prop="businessLicenseUrl">
          <el-upload
            :class="{ hide: hideUpload }"
            class="avatar-uploader"
            :action="`${BASE_URL}/axy/file/uploadFile`"
            :on-success="handleSuccess"
            :limit="1"
            :before-upload="handleUpload"
            accept="image/jpeg,image/png,image/jpg"
            with-credentials
            :multiple="false"
            :file-list="fileList"
            list-type="picture-card"
            :on-remove="() => (form.businessLicenseUrl = '')"
          >
            <i class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
          <span v-show="!hideUpload" class="uploadTips">
            请上传营业执号清晰可见的相关营业执照照片，仅支持jpg、png、jpeg格式的图片，且单张大小不能超过10M</span
          >
        </el-form-item>

        <el-form-item label="" prop="checked">
          <el-checkbox v-model="form.checked">已阅读并同意</el-checkbox>
          <a class="fileLink" href="/agreement/用户协议.pdf" target="_blank">
            《用户协议》
          </a>
          <a
            class="fileLink"
            href="/agreement/办伴隐私政策.pdf"
            target="_blank"
          >
            《办伴隐私政策》
          </a>
        </el-form-item>
      </el-form>
      <div class="btnBox">
        <el-button
          v-if="isEdit"
          type="primary"
          @click="onSubmit()"
          :loading="submitLoading"
          >提交</el-button
        >
        <el-button v-else type="warning" @click="onEdit()">修改</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import * as api from "@/api/userInfo";
export default {
  name: "UserInfo",
  data() {
    const validatorChecked = (rule, value, callback) => {
      if (!value) {
        return callback(new Error("请阅读并勾选同意协议"));
      }
      callback();
    };
    return {
      BASE_URL: process.env.VUE_APP_SERVER_URL,
      fileList: [],
      fileList1: [],
      rules: {
        shopName: [{ required: true, message: "请输入店铺名称", trigger: "blur" }],
        serviceTypeId: [{ required: true, message: "请输入房屋类型", trigger: "blur" }],
        mainBusiness: [
          { required: true, message: "请输入主营业务", trigger: "blur" },
        ],
        description: [
          { required: true, message: "请输入店铺简介", trigger: "blur" },
        ],
        address: [
          { required: true, message: "请输入店铺地址", trigger: "blur" },
        ],
        imageUrl: [
          { required: true, message: "请上传门店照片", trigger: "blur" },
        ],
        contactPerson: [
          { required: true, message: "请输入联系人姓名", trigger: "blur" },
        ],
        phone: [
          { required: true, message: "请输入联系人电话", trigger: "blur" },
          {
            pattern: /^1[3-9]\d{9}$/,
            message: "手机号格式不正确",
            trigger: "blur",
          },
        ],
        businessLicenseUrl: [
          { required: true, message: "请上传营业执照", trigger: "blur" },
        ],
        checked: [{ validator: validatorChecked, trigger: "blur" }],
      },
      isEdit: false,
      submitLoading: false,
      pageLoading: false,
      form: {
        checked: false,
        imageUrl: "",
        businessLicenseUrl: "",
        phone: "",
        contactPerson: "",
        mainBusiness: "",
        description: "",
        address: "",
        shopName: "",
      },
      // baseTypeOptions: [],
      // provinceOptions: [],
      // cascaderTypeProps: {
      //   lazy: true,
      //   value: "id",
      //   label: "baseTypeName",
      //   lazyLoad: async (node, resolve) => {
      //     const { level, value } = node;
      //     console.log(node);
      //     if (level === 1) {
      //       const res = await this.getServiceTypeByBaseType(value);
      //       resolve(res);
      //     } else {
      //       resolve();
      //     }
      //   },
      // },
      // cascaderAddressProps: {
      //   lazy: true,
      //   value: "code",
      //   label: "name",
      //   lazyLoad: async (node, resolve) => {
      //     const { level, value } = node;
      //     console.log(node);
      //     if (level === 1) {
      //       const res = await this.getCity(value);
      //       resolve(res);
      //     } else if (level === 2) {
      //       const res = await this.getArea(value);
      //       resolve(res);
      //     } else {
      //       resolve();
      //     }
      //   },
      // },
    };
  },
  created() {
    // this.getAllBaseType();
    // this.getProvince();
    this.getShopInfo();
  },
  computed: {
    // filterStatus() {
    //   let obj = {
    //     text: "注册未填写资质",
    //     type: "",
    //   };
    //   switch (this.form.status) {
    //     case 0:
    //       obj = {
    //         text: "注册未填写资质",
    //         type: "",
    //       };
    //       break;
    //     case 1:
    //       obj = {
    //         text: "资质信息提交未审核",
    //         type: "info",
    //       };
    //       break;
    //     case 2:
    //       obj = {
    //         text: "审核通过",
    //         type: "success",
    //       };
    //       break;
    //     case 3:
    //       obj = {
    //         text: "审核驳回",
    //         type: "danger",
    //       };
    //       break;
    //     case 4:
    //       obj = {
    //         text: "服务暂停",
    //         type: "warning",
    //       };
    //       break;
    //     default:
    //       obj = {
    //         text: "注册未填写资质",
    //         type: "",
    //       };
    //       break;
    //   }
    //   return obj;
    // },
    hideUpload() {
      return this.fileList.length >= 1 && this.form.businessLicenseUrl;
    },
    hideUpload1() {
      return this.fileList1.length >= 1 && this.form.imageUrl;
    },
  },
  methods: {
    async getShopInfo() {
      try {
        this.pageLoading = true;
        const { data } = await api.getShopInfo();
        console.log(data);
        this.form = data;
        if (data.id) {
          this.fileList = [
            {
              status: "success",
              percentage: 100,
              url: data.businessLicenseUrl,
              response: {
                code: 200,
                data: data.businessLicenseUrl,
              },
            },
          ];
          this.fileList1 = [
            {
              status: "success",
              percentage: 100,
              url: data.imageUrl,
              response: {
                code: 200,
                data: data.imageUrl,
              },
            },
          ];
          this.form = {
            ...data,
            checked: true,

            typeText: `${data.typeName}/${data.baseTypeName}`,
            addressText: `${data.provinceName}/${data.cityName}${
              data.areaName ? "/" + data.areaName : ""
            }`,
          };
          this.isEdit = false;
        } else {
          this.isEdit = true;
          this.form = {
            checked: false,
            imageUrl: "",
            phone: "",
            contactPerson: "",
            businessLicenseUrl: "",
            mainBusiness: "",
            description: "",
            address: "",
            shopName: "",
            openKey: data.openKey,
          };
          this.fileList = [];
          this.fileList1 = [];
        }
      } catch (err) {
        console.error(err);
      } finally {
        this.pageLoading = false;
      }
    },

    handleSuccess(response, file, fileList) {
      console.log("handleSuccess==>", response, file, fileList);
      const { code, data, message } = response;
      if (code == 200) {
        this.form.businessLicenseUrl = data;
        this.fileList = fileList;
        console.log(this.form.businessLicenseUrl);
      } else {
        this.$message.error(message || "上传失败");
      }
    },
    handleSuccess1(response, file, fileList) {
      console.log("handleSuccess==>", response, file, fileList);
      const { code, data, message } = response;
      if (code == 200) {
        this.form.imageUrl = data;
        this.fileList1 = fileList;
        console.log(this.form.imageUrl);
      } else {
        this.$message.error(message || "上传失败");
      }
    },
    handleUpload(file) {
      console.log("handleUpload==>", file);
      const isJPG = ["image/jpg", "image/png", "image/jpeg"].includes(
        file.type
      );
      const isLt2M = file.size / 1024 / 1024 < 10;

      if (!isJPG) {
        return this.$message.error("上传头像图片只能是 JPG 格式!");
      }
      if (!isLt2M) {
        return this.$message.error("上传头像图片大小不能超过 10MB!");
      }
    },
    onEdit() {
      this.isEdit = true;
      window.scrollTo({
        top: 0,
        behavior: "smooth",
      });
    },
    async onSubmit() {
      await this.$refs.form.validate();
      // const postData = { ...this.form };

      try {
        this.submitLoading = true;
        const { data } = await api.setShopInfo(this.form);
        console.log(data);
        this.$message({
          type: "success",
          message: "提交成功",
        });
        this.getShopInfo();
      } catch (err) {
        console.error(err);
      } finally {
        this.submitLoading = false;
      }
    },

    // 省
    // async getProvince() {
    //   try {
    //     const { data } = await api.getProvince();
    //     console.log(data);
    //     this.provinceOptions = data;
    //   } catch (err) {
    //     console.error(err);
    //   }
    // },
    // 市
    // async getCity(value) {
    //   try {
    //     const { data } = await api.getCity(value);
    //     console.log(data);
    //     return Promise.resolve(data);
    //   } catch (err) {
    //     console.error(err);
    //     return Promise.reject();
    //   }
    // },
    // 区
    // async getArea(value) {
    //   try {
    //     const { data } = await api.getArea(value);
    //     console.log(data);
    //     return Promise.resolve(
    //       data.map((e) => {
    //         return {
    //           ...e,
    //           leaf: true,
    //         };
    //       })
    //     );
    //   } catch (err) {
    //     console.error(err);
    //     return Promise.reject();
    //   }
    // },

    // async getAllBaseType() {
    //   try {
    //     const { data } = await api.getAllBaseType();
    //     console.log(data);
    //     this.baseTypeOptions = data;
    //   } catch (err) {
    //     console.error(err);
    //   }
    // },

    // async getServiceTypeByBaseType(baseTypeId) {
    //   try {
    //     const { data } = await api.getServiceTypeByBaseType(baseTypeId);
    //     console.log(data);
    //     return Promise.resolve(
    //       data.map((e) => {
    //         return {
    //           ...e,
    //           leaf: true,
    //           baseTypeName: e.serviceTypeName,
    //         };
    //       })
    //     );
    //   } catch (err) {
    //     console.error(err);
    //     return Promise.reject();
    //   }
    // },
  },
};
</script>

<style lang="scss" scoped>
.wrap {
  .title {
    font-size: 30px;
    line-height: 1.5;
    font-weight: 500;
    padding-bottom: 10px;
  }

  .form {
    width: 800px;

    .formTitle {
      font-size: 18px;
      line-height: 1.5;
      padding: 20px 0;
      font-weight: 600;
    }

    .uploadTips {
      color: #666;
      font-size: 12px;
    }

    .preview {
      position: relative;
      width: 100px;
      height: 100px;

      .model {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
      }

      img {
        display: block;
        object-fit: contain;
        width: 100%;
        height: 100%;
      }

      .el-icon-delete {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        cursor: pointer;
        color: #fff;
      }
    }
  }
}

::v-deep .hide .el-upload--picture-card {
  display: none;
}

.fileLink {
  padding: 0 10px;
  color: #409eff;
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}

::v-deep .el-cascader {
  width: 100% !important;
}

.btnBox {
  padding-left: 150px;
}

.flex {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;

  > div {
    flex: 1;
  }
}
</style>
