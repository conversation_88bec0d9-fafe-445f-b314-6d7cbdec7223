/*
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-05-20 15:59:55
 * @LastEditors: wangxingyue
 * @LastEditTime: 2025-05-22 14:30:22
 * @FilePath: \ban-ban\大业主\admin\src\api\owner.js
 */
import request from "@/utils/request";

// 获取当前业主资料
export function getOwnerInfo() {
  return request({
    url: "/wy/ownerInfo/getOwnerInfo",
    method: "get",
  });
}

// 设置业主资料
export function setOwnerInfo(data) {
  return request({
    url: "/wy/ownerInfo/setOwnerInfo",
    method: "post",
    requestType: "json",
    data
  });
}











// //获取省接口
// export function getProvince() {
//   return request({
//     url: "/area/getProvince",
//     method: "get",
//   });
// }

// //获取市接口
// export function getCity(parentCode) {
//   return request({
//     url: "/area/getCity",
//     method: "get",
//     params: { parentCode },
//   });
// }
// //获取区接口
// export function getArea(parentCode) {
//   return request({
//     url: "/area/getArea",
//     method: "get",
//     params: { parentCode },
//   });
// }

