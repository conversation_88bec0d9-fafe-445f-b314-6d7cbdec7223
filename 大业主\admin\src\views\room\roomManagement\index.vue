<template>
  <div class="app-container">
    <template v-if="isDetail">
      <!-- 添加router-view用于显示子路由内容 -->
      <router-view />
    </template>
    <template v-else>
      <div class="head">
        <el-form inline>
          <el-form-item label="房间号：">
            <el-input
              v-model="querySearch.roomNo"
              placeholder="请输入房间号"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="fetchData()">搜索</el-button>
          </el-form-item>
        </el-form>
        <!-- <div class="btnBox">
          <el-upload
            style="display: inline-block"
            :action="`${BASE_URL}/sw/wy/project/room/importProjectRoom`"
            :on-success="handleSuccess"
            :limit="1"
            :before-upload="handleUpload"
            accept="*"
            with-credentials
            ::multiple="false"
            :show-file-list="false"
            name="file"
            :headers="{
              token,
            }"
          >
            <el-button size="medium" type="success">导入房屋</el-button>
          </el-upload>
          &nbsp;&nbsp;
          <el-button size="medium" type="" @click="downloadTemplate()"
            >下载模版</el-button
          >
          
        </div> -->
      </div>

      <el-table
        v-loading="listLoading"
        :data="list"
        element-loading-text="Loading"
        border
        fit
        highlight-current-row
      >
        <el-table-column type="index" width="50" align="center" />
        <el-table-column label="项目名称" align="center" prop="projectName" 
          :filters="projectNameOptions"
          :filter-method="filterProjectName"
          filter-placement="bottom-end"
          :filter-multiple="false"
          @filter-change="handleProjectNameFilterChange"
        />
        <el-table-column label="楼号" align="center" prop="buildingNumber" />
        <el-table-column label="房间号" align="center" prop="roomNumber" />
        <el-table-column label="面积(平方米)" align="center" prop="area" />
        <el-table-column
          label="电表号"
          align="center"
          prop="electricityMeterNumber"
        />
        <el-table-column label="状态" align="center"
          :filters="statusFilterOptions"
          :filter-method="filterStatus"
          filter-placement="bottom-end"
          :filter-multiple="false"
          @filter-change="handleStatusFilterChange">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="报价(元/月)" align="center" prop="price" />
        <el-table-column label="采集器号" align="center" prop="electricityMeterCid" />
        <el-table-column label="电表地址" align="center" prop="electricityMeterAddress" />
        <el-table-column label="房间图片" align="center">
          <template #default="{ row }">
            <el-image
              v-if="row.imageUrl"
              style="width: 50px; height: 50px"
              :src="row.imageUrl"
              :preview-src-list="[row.imageUrl]"
            >
            </el-image>
            <span v-else>在修改中添加图片</span>
          </template>
        </el-table-column>
        <el-table-column label="房间视频" align="center">
          <template #default="{ row }">
            <el-button
              v-if="row.videoUrl"
              type="text"
              @click="previewVideo(row.videoUrl)"
            >
              查看视频
            </el-button>
            <span v-else>在修改中添加视频</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="{ row }">
            <el-button type="text" @click="openActivity(row)">修改</el-button>
            <el-button
              type="text"
              :class="{ 'active-button': isButtonActive(row.id, 'lease') }"
              @click="handleButtonClick(row, 'lease', openLease)"
            >
              租赁管理
            </el-button>
            <el-button
              type="text"
              :class="{ 'active-button': isButtonActive(row.id, 'billing') }"
              @click="handleButtonClick(row, 'billing', openBilling)"
            >
              账单管理
            </el-button>
            <el-button
              type="text"
              :class="{ 'active-button': isButtonActive(row.id, 'history') }"
              @click="handleButtonClick(row, 'history', openLeaseHistorical)"
            >
              租赁历史
            </el-button>
            <el-button
              type="text"
              :class="{ 'active-button': isButtonActive(row.id, 'edit-history') }"
              @click="handleButtonClick(row, 'edit-history', openEditHistorical)"
            >
              修改历史
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          @size-change="fetchData"
          @current-change="fetchData"
          :current-page.sync="querySearch.pageNo"
          :page-sizes="[15, 30, 50, 100]"
          :page-size.sync="querySearch.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="querySearch.total"
        >
        </el-pagination>
      </div>

      <el-drawer
        title="修改房间信息"
        :visible.sync="drawerShow"
        direction="rtl"
        size="800px"
      >
        <div class="drawer">
          <el-form
            ref="form"
            :model="form"
            label-width="200px"
            size="small"
            :rules="rules"
            :disabled="submitLoading"
          >
            <el-form-item label="项目名称：">
              <el-input v-model="form.projectName"></el-input>
            </el-form-item>

            <el-form-item label="楼号：">
              <el-input v-model="form.buildingNumber"></el-input>
            </el-form-item>

            <el-form-item label="房间号：">
              <el-input v-model="form.roomNumber"></el-input>
            </el-form-item>

            <el-form-item label="面积(平方米)：">
              <el-input v-model="form.area"></el-input>
            </el-form-item>

            <el-form-item label="状态：">
              <el-select v-model="form.status" placeholder="请选择状态">
                <el-option
                  v-for="(item, index) in statuslist"
                  :key="index + item.value"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="报价(元/月)：">
              <el-input v-model="form.price"></el-input>
            </el-form-item>

            <el-form-item label="采集器号：">
              <el-input v-model="form.electricityMeterCid"></el-input>
            </el-form-item>

            <el-form-item label="电表地址：">
              <el-input v-model="form.electricityMeterAddress"></el-input>
            </el-form-item>

            <el-form-item label="电表号：">
              <el-input v-model="form.electricityMeterNumber"></el-input>
            </el-form-item>

            <el-form-item label="房间图片：" prop="imageUrl">
              <el-upload
                :class="{ 'hide-upload-btn': hideUpload }"
                class="avatar-uploader"
                :action="`${BASE_URL}/axy/file/uploadFile`"
                :on-success="handleSuccess1"
                :limit="1"
                :before-upload="handleUpload1"
                accept="image/jpeg,image/png,image/jpg"
                with-credentials
                :multiple="false"
                :file-list="fileList"
                list-type="picture-card"
                :headers="{ token }"
                :on-exceed="handleExceed"
                :on-remove="
                  () => {
                    form.imageUrl = '';
                    fileList = [];
                  }
                "
              >
                <i class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
              <span class="uploadTips">
                仅支持jpg、png、jpeg格式的图片，且单张大小不能超过10M</span
              >
            </el-form-item>

            <el-form-item label="房间视频：" prop="videoUrl">
              <el-upload
                :class="{ 'hide-upload-btn': hideVideoUpload }"
                class="video-uploader"
                :action="`${BASE_URL}/axy/file/uploadFile`"
                :on-success="handleVideoSuccess"
                :limit="1"
                :before-upload="beforeVideoUpload"
                accept="video/mp4,video/avi,video/mov"
                with-credentials
                :multiple="false"
                :file-list="videoFileList"
                list-type="text"
                :headers="{ token }"
                :on-exceed="handleExceed"
                :on-remove="
                  () => {
                    form.videoUrl = '';
                    videoFileList = [];
                  }
                "
              >
                <el-button size="small" type="primary">点击上传视频</el-button>
              </el-upload>
              <el-button
                v-if="form.videoUrl"
                type="text"
                @click="previewVideoInForm"
                style="margin-left: 10px"
              >
                预览视频
              </el-button>
              <span class="uploadTips">
                仅支持mp4、avi、mov格式的视频，且单个大小不能超过50M</span
              >
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                @click="onSubmit()"
                :loading="submitLoading"
                >提交</el-button
              >
            </el-form-item>
          </el-form>
        </div>
      </el-drawer>
    </template>
  </div>
</template>

<script>
import * as api from "@/api/room";
import { getAllProject } from "@/api/project"

import { getToken } from "@/utils/auth";

// 状态处理混入
const statusMixin = {
  methods: {
    getStatusText(status) {
      return status === 0 || status === "0" ? "已租" : "待租";
    },

    getStatusType(status) {
      return status === 0 || status === "0" ? "success" : "info";
    },

    // 将任意状态值转换为数字类型
    normalizeStatus(status) {
      return status === 0 || status === "0" ? 0 : 1;
    },
  },
};

export default {
  name: 'RoomManagement',
  mixins: [statusMixin],
  data() {
    return {
      BASE_URL: process.env.VUE_APP_SERVER_URL,
      list: null,
      listLoading: true,
      querySearch: {
        pageNo: 1,
        pageSize: 50,
        total: 0,
        roomNo: null,
        projectId: -1, // 默认查询全部项目
        status: -1, // 默认查询全部状态
      },
      projectNameOptions: [], // 项目名称选项
      statusFilterOptions: [
        { text: "已租", value: 0 },
        { text: "待租", value: 1 }
      ],
      form: {
        id: "",
        openKey: "",
        projectName: "",
        buildingNumber: "",
        roomNumber: "",
        area: "",
        status: "",
        statusText: "",
        electricityMeterNumber: "",
        electricityMeterCid: "",
        electricityMeterAddress: "",
        price: "",
        imageUrl: "",
        videoUrl: "",
      },
      fileList: [],
      videoFileList: [],
      videoFileName: "",

      // 持久化按钮状态管理 - 全局互斥模式
      activeButton: null, // 格式: { roomId: 'xxx', buttonType: 'xxx' } 或 null

      rules: {
        imageUrl: [
          { required: false, message: "请上传房间图片", trigger: "change" },
        ],
        videoUrl: [
          { required: false, message: "请上传房间视频", trigger: "change" },
        ],
      },
      submitLoading: false,
      drawerShow: false,
      statusOptions: [0, 1], // 状态选项值改为数字类型
    };
  },
  mounted() {
    this.fetchData();
    this.loadButtonStates();
    this.getProjectList(); // 获取项目列表
  },
  activated() {
    this.fetchData();
    this.loadButtonStates();
  },
  computed: {
    isDetail() {
      return this.$route.name !== "RoomManagement";
    },
    token() {
      return getToken();
    },
    hideUpload() {
      return this.fileList.length >= 1 || this.form.imageUrl;
    },
    hideVideoUpload() {
      return this.videoFileList.length >= 1 || this.form.videoUrl;
    },
    // 生成状态列表
    statuslist() {
      return this.statusOptions.map((value) => ({
        name: this.getStatusText(value),
        value: value,
      }));
    },
  },
  methods: {
    async fetchData() {
      try {
        this.listLoading = true;
        // 构建查询参数
        const queryParams = {
          pageNo: this.querySearch.pageNo,
          pageSize: this.querySearch.pageSize,
        };

        // 添加房间号参数（如果有值）
        if (this.querySearch.roomNo) {
          queryParams.roomNo = this.querySearch.roomNo;
        }

        // 添加项目ID参数（-1表示全部项目，不传该参数）
        if (this.querySearch.projectId !== -1) {
          queryParams.projectId = this.querySearch.projectId;
        }

        // 添加状态参数（-1表示全部状态，不传该参数）
        if (this.querySearch.status !== -1) {
          queryParams.status = this.querySearch.status;
        }

        console.log('查询参数:', queryParams);

        const { data: res } = await api.getRoomList(queryParams);
        console.log('接口返回:', res);

       const { data, pageNo, pageSize, totalElements } = res;
          this.list = data || [];
          this.querySearch = {
            ...this.querySearch,
            pageNo,
            pageSize,
            total: totalElements,
          };
      } catch (err) {
        console.error('获取房间列表失败:', err);
        this.$message.error('获取房间列表失败');
        this.list = [];
      } finally {
        this.listLoading = false;
      }
    },
    
    // 获取所有项目列表
    async getProjectList() {
      try {
        const res = await getAllProject();
        console.log('获取项目列表结果:', res);

        // 使用项目ID作为筛选值，而不是项目名称
          this.projectNameOptions = res.data.map(item => ({
            text: item.projectName,
            value: item.id
          }));
          console.log('项目筛选选项:', this.projectNameOptions);
      } catch (error) {
        console.error('获取项目列表异常:', error);
        this.projectNameOptions = [];
        this.$message.error('获取项目列表失败');
      }
    },
    
    // 项目名称过滤方法
    filterProjectName(value, row) {
      
      return true;
    },

    // 处理项目名称筛选变化
    handleProjectNameFilterChange(filters) {
      console.log('handleProjectNameFilterChange', filters);
    },

    // 状态过滤方法
    filterStatus(value, row) {
      
      return true;
    },

    // 处理状态筛选变化
    handleStatusFilterChange(filters) {
      console.log('handleStatusFilterChange', filters);
    },
    
    downloadTemplate() {
      window.open("/xlsx/房间信息模板.xlsx", "_blank");
    },

    async openActivity(row = {}) {
      if (row.id) {
        //编辑
        const status = row.status;
        console.log("接口返回的status值:", status);
        console.log("接口返回的status类型:", typeof status);

        // 确保status值为数字类型
        const formStatus = this.normalizeStatus(status);
        console.log("转换后的status值:", formStatus);
        console.log("状态文本:", this.getStatusText(status));

        this.form = {
          ...row,
          openKey: row.openKey || "",
          status: formStatus, // 使用转换后的数字状态值
        };

        // 初始化文件列表用于回显
        this.fileList = [];
        this.videoFileList = [];
        this.videoFileName = "";

        // 如果有图片，添加到文件列表
        if (this.form.imageUrl) {
          this.fileList = [
            {
              name: "房间图片",
              url: this.form.imageUrl,
            },
          ];
        }

        // 如果有视频，添加到视频文件列表
        if (this.form.videoUrl) {
          this.videoFileList = [
            {
              name: "房间视频",
              url: this.form.videoUrl,
            },
          ];
          this.videoFileName = "房间视频";
        }
      }
      this.drawerShow = true;
    },
    // handleSuccess(response, file, fileList) {
    //   console.log("handleSuccess==>", response, file, fileList);
    //   const { code, data, message } = response;
    //   if (code == 200) {
    //     this.$message.success("导入成功！");
    //     setTimeout(() => {
    //       this.fetchData();
    //     }, 500);
    //   } else {
    //     this.$message.error(message || "导入失败");
    //   }
    // },
    // handleUpload(file) {
    //   console.log("handleUpload==>", file);
    //   const is_xlsx = file.name.indexOf(".xlsx") !== -1;
    //   if (!is_xlsx) {
    //     return this.$message.error("上传文件只能是 xlsx格式 !");
    //   }
    // },

    handleSuccess1(response, file, fileList) {
      console.log("handleSuccess==>", response, file, fileList);
      const { code, data, message } = response;
      if (code == 200) {
        this.form.imageUrl = data;
        console.log(this.form.imageUrl);
      } else {
        this.$message.error(message || "上传失败");
      }
    },
    handleUpload1(file) {
      console.log("handleUpload==>", file);
      const isJPG = ["image/jpg", "image/png", "image/jpeg"].includes(
        file.type
      );
      const isLt2M = file.size / 1024 / 1024 < 10;

      if (!isJPG) {
        return this.$message.error("上传图片只能是 JPG/PNG/JPEG 格式!");
      }
      if (!isLt2M) {
        return this.$message.error("上传图片大小不能超过 10MB!");
      }
    },

    handleVideoSuccess(response, file, fileList) {
      console.log("handleVideoSuccess==>", response, file, fileList);
      const { code, data, message } = response;
      if (code == 200) {
        this.form.videoUrl = data;
        this.videoFileName = file.name;
        console.log(this.form.videoUrl);
        // 替换视频文件的预览效果
        this.videoFileList = [
          {
            name: file.name,
            url: data,
          },
        ];
      } else {
        this.$message.error(message || "上传失败");
      }
    },
    beforeVideoUpload(file) {
      console.log("beforeVideoUpload==>", file);
      const isVideo = ["video/mp4", "video/avi", "video/mov"].includes(
        file.type
      );
      const isLt50M = file.size / 1024 / 1024 < 50;

      if (!isVideo) {
        return this.$message.error("上传视频只能是 MP4/AVI/MOV 格式!");
      }
      if (!isLt50M) {
        return this.$message.error("上传视频大小不能超过 50MB!");
      }
    },

    previewVideo(url) {
      window.open(url, "_blank");
    },

    previewVideoInForm() {
      this.$notify({
        title: "视频预览",
        message: this.$createElement(
          "div",
          {
            style: {
              width: "100%",
              textAlign: "center",
            },
          },
          [
            this.$createElement("video", {
              attrs: {
                src: this.form.videoUrl,
                controls: true,
                autoplay: false,
                style: "width: 100%; max-height: 400px;",
              },
            }),
          ]
        ),
        duration: 0,
        customClass: "video-preview-notification",
        position: "bottom-right",
      });
    },

    async onSubmit() {
      await this.$refs.form.validate();
      // 确保status值为数字类型
      const statusValue = this.normalizeStatus(this.form.status);

      const postData = {
        id: this.form.id,
        openKey: this.form.openKey,
        projectName: this.form.projectName,
        buildingNumber: this.form.buildingNumber,
        roomNumber: this.form.roomNumber,
        area: this.form.area,
        status: statusValue, // 使用转换为数字的状态值
        electricityMeterNumber: this.form.electricityMeterNumber,
        electricityMeterCid: this.form.electricityMeterCid,
        electricityMeterAddress: this.form.electricityMeterAddress,
        price: this.form.price,
        imageUrl: this.form.imageUrl,
        videoUrl: this.form.videoUrl,
      };

      try {
        this.submitLoading = true;
        const res = await api.modifyProjectRoom(postData);
        if (res.code === 200) {
          this.$message.success("修改成功");
          this.drawerShow = false;
          this.fetchData();
        } else {
          this.$message.error(res.message || "修改失败");
        }
      } catch (err) {
        console.error(err);
        this.$message.error("修改失败");
      } finally {
        this.submitLoading = false;
      }
    },

    // 租赁
    openLease({ id }) {
      // 使用 router.resolve 生成完整的路由URL
      const routeData = this.$router.resolve({
        name: "RoomManagementLease",
        params: {
          id: id,
        },
      });
      // 在新标签页中打开
      window.open(routeData.href, "_blank");
    },

    // 账单
    openBilling({ id }) {
      // 使用 router.resolve 生成完整的路由URL
      const routeData = this.$router.resolve({
        name: "RoomManagementBill",
        params: {
          id: id,
        },
      });
      // 在新标签页中打开
      window.open(routeData.href, "_blank");
    },

    // 租赁历史记录
    openLeaseHistorical({ id }) {
      // 使用 router.resolve 生成完整的路由URL
      const routeData = this.$router.resolve({
        name: "RoomManagementHistorical",
        params: {
          id: id,
        },
      });
      // 在新标签页中打开
      window.open(routeData.href, "_blank");
    },

    // 修改历史记录
    openEditHistorical({ id }) {
      // 使用 router.resolve 生成完整的路由URL
      const routeData = this.$router.resolve({
        name: "RoomManagementEdithistorical",
        params: {
          id: id,
        },
      });
      // 在新标签页中打开
      window.open(routeData.href, "_blank");
    },

    // 持久化状态管理方法
    loadButtonStates() {
      try {
        const savedState = localStorage.getItem('room_active_button');
        if (savedState) {
          const parsedState = JSON.parse(savedState);
          // 确保加载的状态格式正确
          if (parsedState && parsedState.roomId && parsedState.buttonType) {
            this.activeButton = parsedState;
            console.log('成功加载按钮状态:', JSON.stringify(this.activeButton));
          } else {
            this.activeButton = null;
          }
        } else {
          this.activeButton = null;
        }
      } catch (error) {
        console.error('加载按钮状态失败:', error);
        this.activeButton = null;
      }
    },

    saveButtonStates() {
      try {
        if (this.activeButton) {
          localStorage.setItem('room_active_button', JSON.stringify(this.activeButton));
        } else {
          localStorage.removeItem('room_active_button');
        }
      } catch (error) {
        console.error('保存按钮状态失败:', error);
      }
    },

    // 统一的按钮点击处理
    handleButtonClick(row, buttonType, originalMethod) {
      // console.log(`点击按钮 - 房间ID: ${row.id}, 按钮类型: ${buttonType}`);
      // console.log('点击前状态:', JSON.stringify(this.activeButton));

      // 全局互斥：设置当前激活的按钮，清除之前的激活状态
      this.activeButton = {
        roomId: row.id,
        buttonType: buttonType
      };

      // console.log('点击后状态:', JSON.stringify(this.activeButton));

      // 保存状态到localStorage
      this.saveButtonStates();

      // 调用原始方法
      originalMethod(row);
    },

    // 检查按钮是否激活
    isButtonActive(roomId, buttonType) {
      const isActive = this.activeButton &&
                      this.activeButton.roomId === roomId &&
                      this.activeButton.buttonType === buttonType;
      // console.log(`检查按钮状态 - 房间ID: ${roomId}, 按钮类型: ${buttonType}, 激活状态: ${isActive}`);
      return isActive;
    },

    // 清除当前激活的按钮状态
    clearActiveButtonState() {
      this.activeButton = null;
      this.saveButtonStates();
    },

    // 获取当前激活的按钮信息
    getActiveButton() {
      return this.activeButton;
    },

    // 检查特定房间是否有激活按钮
    isRoomActive(roomId) {
      return this.activeButton && this.activeButton.roomId === roomId;
    },


    handleExceed() {
      this.$message.warning("最多只能上传1个文件");
    },
  },
};
</script>

<style scoped lang="scss">
.pagination {
  padding-top: 20px;
  text-align: right;
}
.drawer {
  padding: 0 20px;
}
.head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .btnBox {
    > button {
      margin-right: 10px;
    }
    > div {
      margin-right: 10px;
      margin-bottom: 20px;
    }
  }
}

.uploadTips {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
  display: block;
}

.avatar-uploader,
.video-uploader {
  margin-bottom: 10px;
}

::v-deep .avatar-uploader .el-upload,
::v-deep .video-uploader .el-upload {
  // border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  display: flex;
  // justify-content: center;
  align-items: center;
}

::v-deep .avatar-uploader .el-upload:hover,
::v-deep .video-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

::v-deep .hide-upload-btn .el-upload--picture-card {
  display: none;
}

::v-deep .video-preview-notification {
  width: 400px;
}

/* 激活按钮样式 */
.active-button {
  color: #0881fa !important;
  font-weight: 600 !important;
  transition: all 0.3s ease;
}

.active-button:hover {
  color: #0881fa !important;
}
</style>
