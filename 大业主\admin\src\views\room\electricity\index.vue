<!--
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-07-17 10:10:08
 * @LastEditors: wangxingyue
 * @LastEditTime: 2025-07-23 14:26:30
 * @FilePath: \ban-ban\大业主\admin\src\views\room\electricity\index.vue
-->
<template>
  <div class="app-container">
    <!-- <div class="dashboard-section">
      <div class="section-header">
        <i class="el-icon-data-analysis"></i>
        <span>项目用电量详情</span>
      </div> -->

      <el-table
        v-loading="listLoading"
        :data="list"
        max-height="580"
        style="width: 100%"
        element-loading-text="Loading"
        border
        fit
        highlight-current-row
        @filter-change="handleFilterChange"
        :default-filter="activeFilters"
        class="main-table"
      >
        <el-table-column label="日期" align="center" prop="date">
          <template #default="{ row }">{{
            parseTime(row.date, "YYYY-MM-DD")
          }}</template>
        </el-table-column>
        <el-table-column
          label="项目名称"
          align="center"
          prop="projectName"
          column-key="projectName"
          :filters="projectNameOptions"
          :filter-multiple="false"
          filter-placement="bottom-end"
        />
        <el-table-column label="总房间数" align="center" prop="totalRooms" />
        <el-table-column label="已租房间数" align="center" prop="rentRooms" />
        <el-table-column label="待租房间数" align="center" prop="waitRooms" />
        <el-table-column label="总用电量" align="center" prop="totalUsed" />
        <el-table-column label="总用电金额" align="center" prop="totalMoney" />
        <el-table-column label="预警数量" align="center" prop="alertNumber">
          <template #default="{ row }">
            <el-button type="text" @click="handleViewAlert(row)">{{
              row.alertNumber
            }}</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          @size-change="fetchData"
          @current-change="fetchData"
          :current-page.sync="querySearch.pageNo"
          :page-sizes="[15, 30, 50, 100]"
          :page-size.sync="querySearch.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="querySearch.total"
        >
        </el-pagination>
      </div>
    <!-- </div> -->

    <!-- 预警数据弹窗 -->
    <el-dialog
      title="预警数据详情"
      :visible.sync="alertDialogVisible"
      width="50%"
      custom-class="alert-dialog"
    >
      <el-table
        v-loading="alertLoading"
        :data="alertList"
        max-height="500"
        border
        fit
        highlight-current-row
      >
        <el-table-column type="index" width="50" align="center" />
        <el-table-column label="预警日期" align="center" prop="date">
          <template #default="{ row }">{{
            parseTime(row.date, "YYYY-MM-DD")
          }}</template>
        </el-table-column>
        <el-table-column label="房间号" align="center" prop="roomNo" />
        <el-table-column label="项目名称" align="center" prop="projectName" />
        <el-table-column label="原因" align="center" prop="reason" />
      </el-table>
      
      <div class="pagination">
        <el-pagination
          @size-change="fetchAlertData"
          @current-change="fetchAlertData"
          :current-page.sync="alertQuery.pageNo"
          :page-sizes="[10, 20, 30, 50]"
          :page-size.sync="alertQuery.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="alertQuery.total"
        >
        </el-pagination>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import * as api from "@/api/room";
import { getAllProject } from "@/api/project";
export default {
  name: "Electricity",
  data() {
    return {
      list: null,
      listLoading: true,
      projectNameOptions: [], // 项目名称筛选选项
      activeFilters: {
        projectName: [], // 当前激活的筛选条件
      },
      querySearch: {
        pageNo: 1,
        pageSize: 50,
        total: 0,
        projectId: -1, // 默认查询全部项目
      },
      // 预警数据相关
      alertDialogVisible: false,
      alertLoading: false,
      alertList: [],
      alertQuery: {
        reportId: "",
        pageNo: 1,
        pageSize: 50,
        total: 0,
      },
    };
  },
  created() {
    this.getProjectList();
    this.fetchData();
  },
  methods: {
    // 获取所有项目
    async getProjectList() {
      try {
        const { data } = await getAllProject();
        // 设置项目筛选选项
        this.projectNameOptions = data.map((item) => ({
          text: item.projectName,
          value: item.id,
        }));
        console.log("项目筛选选项:", this.projectNameOptions);
      } catch (err) {
        console.error(err);
        this.$message.error("获取项目列表失败：" + (err.message || "未知错误"));
      }
    },

    // 处理筛选变化
    handleFilterChange(filters) {
      console.log("表格筛选变化:", filters);

      // 处理项目名称筛选
      if ("projectName" in filters) {
        // 更新activeFilters中的projectName
        this.activeFilters.projectName = filters.projectName || [];

        // 如果是项目名称筛选发生变化
        if (filters.projectName && filters.projectName.length > 0) {
          this.querySearch.projectId = filters.projectName[0];
        } else {
          this.querySearch.projectId = -1; // 如果取消选中，则重置为全部项目
        }
      }

      console.log("筛选条件:", {
        项目ID: this.querySearch.projectId,
      });

      // 重置到第一页
      this.querySearch.pageNo = 1;
      this.fetchData();
    },

    async fetchData() {
      try {
        this.listLoading = true;

        // 构建查询参数
        const queryParams = {
          pageNo: this.querySearch.pageNo,
          pageSize: this.querySearch.pageSize,
          projectId: this.querySearch.projectId, // 始终传递projectId，即使是-1
        };

        // 同步更新activeFilters中的projectName
        if (this.querySearch.projectId !== -1) {
          this.activeFilters.projectName = [this.querySearch.projectId];
        } else {
          // 如果查询全部项目，清空projectName筛选
          this.activeFilters.projectName = [];
        }

        console.log("查询参数:", queryParams);

        const { data: res } = await api.getByPage(queryParams);
        const { data, pageNo, pageSize, totalElements } = res;
        this.list = data;
        this.querySearch = {
          ...this.querySearch,
          pageNo,
          pageSize,
          total: totalElements,
        };
      } catch (err) {
        console.error(err);
      } finally {
        this.listLoading = false;
      }
    },

    // 查看预警数据
    handleViewAlert(row) {
      this.alertQuery.reportId = row.id;
      this.alertQuery.pageNo = 1;
      this.alertDialogVisible = true;
      this.fetchAlertData();
    },

    // 获取预警数据
    async fetchAlertData() {
      try {
        this.alertLoading = true;
        const params = {
          reportId: this.alertQuery.reportId,
          pageNo: this.alertQuery.pageNo,
          pageSize: this.alertQuery.pageSize,
        };

        const { data: res } = await api.getAlertHistory(params);
        const { data, pageNo, pageSize, totalElements } = res;
        this.alertList = data;
        this.alertQuery = {
          ...this.alertQuery,
          pageNo,
          pageSize,
          total: totalElements,
        };
      } catch (err) {
        console.error(err);
        this.$message.error("获取预警数据失败：" + (err.message || "未知错误"));
      } finally {
        this.alertLoading = false;
      }
    },
  },
};
</script>

<style scoped lang="scss">
// .dashboard-section {
//   background: #fff;
//   border-radius: 4px;
//   box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
//   padding: 20px;
//   margin-bottom: 20px;
// }

// .section-header {
//   display: flex;
//   align-items: center;
//   margin-bottom: 20px;
//   border-bottom: 1px solid #ebeef5;
//   padding-bottom: 15px;
  
//   i {
//     color: #409EFF;
//     font-size: 20px;
//     margin-right: 8px;
//   }
  
//   span {
//     font-size: 18px;
//     font-weight: 500;
//     color: #303133;
//   }
// }


.pagination {
  padding-top: 20px;
  text-align: right;
}

.main-table {
  width: 100%;
  
  .el-button--text {
    color: #409EFF;
    font-weight: bold;
    
    &:hover {
      color: #66b1ff;
    }
  }
}

.alert-dialog {
  .el-dialog__header {
    background: #409EFF;
    padding: 15px 20px;
    
    .el-dialog__title {
      color: white;
      font-weight: bold;
    }
  }
  
  .el-dialog__headerbtn .el-dialog__close {
    color: white;
  }
}

:deep(.el-tag) {
  padding: 0 10px;
  height: 26px;
  line-height: 24px;
}
</style>
