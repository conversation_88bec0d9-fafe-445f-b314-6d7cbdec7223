/*
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-05-20 15:59:55
 * @LastEditors: wangxingyue
 * @LastEditTime: 2025-07-22 16:34:22
 * @FilePath: \admin\src\router\index.js
 */
/*
 * @Author: wang<PERSON>yue
 * @Date: 2025-05-20 15:59:55
 * @LastEditors: wangxingyue
 * @LastEditTime: 2025-07-22 16:32:48
 * @FilePath: \admin\src\router\index.js
 */
import Vue from "vue";
import Router from "vue-router";

Vue.use(Router);

/* Layout */
import Layout from "@/layout";
import Secondary from "@/layout/secondary";
import Login from "@/views/login/index";
import Refresh from "@/views/refresh.vue";

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'/'el-icon-x' the icon show in the sidebar
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    path: "/login",
    component: Login,
    hidden: true,
  },

  {
    path: "/404",
    component: () => import("@/views/404"),
    hidden: true,
  },
  {
    path: "/",
    redirect: "/data",
    hidden: true,
  },
  {
    path: "/data",
    component: Layout,
    redirect: "/data/owner",
    meta: { title: "资料管理" },
    alwaysShow: true,
    children: [
      {
        path: "owner",
        name: "Owner",
        component: () => import("@/views/data/owner/index"),
        meta: { title: "业主资料", requiredEdit: true },
      },
      {
        path: "project",
        name: "Project",
        component: () => import("@/views/data/project/index"),
        meta: { title: "项目资料", requiredEdit: true },
      }
      // {
      //   path: "uploadroom",
      //   name: "Uploadroom",
      //   component: () => import("@/views/data/uploadroom/index"),
      //   meta: { title: "上传房间", requiredEdit: true },
      // },
    ],
  },
  {
    path: "/room",
    component: Layout,
    redirect: "/room/roomManagement",
    meta: { title: "房间管理" },
    alwaysShow: true,
    children: [
      {
        path: "roomManagement",
        name: "RoomManagement",
        component: () => import("@/views/room/roomManagement/index"),
        meta: { title: "房间管理", requiredEdit: true },
        children: [
          {
            path: "lease/:id",
            name: "RoomManagementLease",
            component: () => import("@/views/room/roomManagement/lease/index"),
            meta: {
              title: "租赁管理",
              activeMenu: "/room/roomManagement",
              // isShowTitleBack: true,
              keepAlive: true
            },
            hidden: true,
          },
          {
            path: "bill/:id",
            name: "RoomManagementBill",
            component: () => import("@/views/room/roomManagement/bill/index"),
            meta: {
              title: "账单管理",
              activeMenu: "/room/roomManagement",
              // isShowTitleBack: true,
              keepAlive: true
            },
            hidden: true,
          },
          {
            path: "historical/:id",
            name: "RoomManagementHistorical",
            component: () => import("@/views/room/roomManagement/historical/index"),
            meta: {
              title: "租赁历史",
              activeMenu: "/room/roomManagement",
              // isShowTitleBack: true,
              keepAlive: true
            },
            hidden: true,
          },
          {
            path: "edithistorical/:id",
            name: "RoomManagementEdithistorical",
            component: () => import("@/views/room/roomManagement/edithistorical/index"),
            meta: {
              title: "修改历史",
              activeMenu: "/room/roomManagement",
              // isShowTitleBack: true,
              keepAlive: true
            },
            hidden: true,
          }
        ],
      },
      {
        path: "electricity",
        name: "Electricity",
        component: () => import("@/views/room/electricity/index"),
        meta: { title: "电表电量", requiredEdit: true },
      },
      {
        path: "inquiry",
        name: "Inquiry",
        component: () => import("@/views/room/inquiry/index"),
        meta: { title: "电量查询", requiredEdit: true },
      },
      {
        path: "notice",
        name: "Notice",
        component: () => import("@/views/room/notice/index"),
        meta: { title: "通知公告", requiredEdit: true },
      }
    ],
  },
  {
    path: "/finance",
    component: Layout,
    redirect: "/finance/withdrawal",
    meta: { title: "财务管理" },
    alwaysShow: true,
    children: [
      {
        path: "withdrawal",
        name: "Withdrawal",
        component: () => import("@/views/finance/withdrawal/index"),
        meta: { title: "账户概况", requiredEdit: true },
      },
      // {
      //   path: "deposit",
      //   name: "deposit",
      //   component: () => import("@/views/finance/deposit/index"),
      //   meta: { title: "押金管理", requiredEdit: true },
      // },
      {
        path: "report",
        name: "report",
        component: () => import("@/views/finance/report/index"),
        meta: { title: "财务报表", requiredEdit: true },
      },
    ],
  },

  {
    path: "/refresh",
    name: "Refresh",
    component: Refresh,
    hidden: true,
  },

  // 404 page must be placed at the end !!!
  { path: "*", redirect: "/404", hidden: true },
];

const createRouter = () =>
  new Router({
    // mode: 'history', // require service support
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRoutes,
  });

const router = createRouter();

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter();
  router.matcher = newRouter.matcher; // reset router
}

export default router;
