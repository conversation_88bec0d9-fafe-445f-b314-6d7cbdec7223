<template>
  <div class="tinymce-container">
    <el-input
      type="textarea"
      :value="value"
      :rows="rows"
      placeholder="请输入内容"
      @input="handleInput"
      class="tinymce-textarea"
    />
  </div>
</template>

<script>
export default {
  name: '<PERSON><PERSON><PERSON>',
  props: {
    value: {
      type: String,
      default: ''
    },
    height: {
      type: [Number, String],
      required: false,
      default: 360
    }
  },
  computed: {
    rows() {
      return parseInt(this.height) / 20 // 粗略估计行高为20px
    }
  },
  methods: {
    handleInput(val) {
      this.$emit('input', val)
    }
  }
}
</script>

<style scoped>
.tinymce-container {
  position: relative;
  line-height: normal;
}
.tinymce-textarea {
  width: 100%;
}
</style> 