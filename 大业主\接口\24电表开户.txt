1、开户

/sw/wy/ele/meter/openAccount
GET
roomId:

响应:
{
	code:200,
	data:null
}

2、开户历史
/sw/wy/ele/meter/getOpenAccountHistory

{
  "reportId": "项目id",数据行的id
  "pageNo": "页码",
  "pageSize": "页大小"
}

响应:
{
	code: 业务响应码 200 500...  
	message: 这个是异常的时候的消息提示
	data:{
		pageNo: '第几页'
		pageSize:'一页多少页'
		totalPages:'总页数'
		totalElements:'总数据量'
		data:[

{
id
  "date": "@Temporal(TemporalType.DATE) 操作日期",
  "projectId": "所属项目",
  "roomId": "所属房间",
  "projectName": "项目名称",
  "roomNo": "房间号",
  "optionType": "0 合闸 1 拉闸 3 清零 4 开户",
  "balance": "操作时余额 (默认: 0)",
  "operator": "操作人"
}
			]
	}
}