1、获取某个房间的用电量
/sw/wy/ele/consump/findByPage

post
参数:
{
  "projectId": "项目id",
  "roomNo":"房屋编号"
  "pageNo": "页码",
  "pageSize": "页大小"
}

响应:
{
	code: 业务响应码 200 500...  
	message: 这个是异常的时候的消息提示
	data:{
		pageNo: '第几页'
		pageSize:'一页多少页'
		totalPages:'总页数'
		totalElements:'总数据量'
		data:[

{
id
  "date": "@Temporal(TemporalType.DATE) 日期",
  "projectId": "项目id",
  "roomId": "房间id",
  "projectName": "项目名",
  "roomNumber": "房间号",
  "collectorNumber": "采集器号",
  "meterAddress": "电表地址",
  "status": "状态",//0 离线  1 在线
  "switchStatus": "拉合闸状态 //0 拉闸  1 合闸",
  "electricityUsage": "用电量(单位：度)",
  "electricityCost": "用电金额",
  "balance": "电费余额"
}penKey": "所属物业"
		}
			]
	}
}


2、获取充值记录
/sw/wy/ele/pay/history/findByPage
post
参数:
{
  "reportId": "项目id",数据行的id
  "pageNo": "页码",
  "pageSize": "页大小"
}

响应:

{
	code: 业务响应码 200 500...  
	message: 这个是异常的时候的消息提示
	data:{
		pageNo: '第几页'
		pageSize:'一页多少页'
		totalPages:'总页数'
		totalElements:'总数据量'
		data:[

{
id:id
  "payName": "支付人",
  "type": "0 房租 1 电费 2水费",
  "amount": "支付金额",
  "payTime": "支付时间",
  "openKey": "所属物业",
  "roomId": "房间id",
  "payType": "0 线下 1 线上",
  "billDate": "@Temporal(TemporalType.DATE) 付款日",
  "projectId": "所属项目"
}
			]
	}
}


3、清零
/sw/wy/ele/meter/clearMeter
GET
roomId:

响应:
{
	code:200,
	data:null
}


4、获取清零历史

/sw/wy/ele/meter/getClearHistory

{
  "reportId": "项目id",数据行的id
  "pageNo": "页码",
  "pageSize": "页大小"
}

响应:
{
	code: 业务响应码 200 500...  
	message: 这个是异常的时候的消息提示
	data:{
		pageNo: '第几页'
		pageSize:'一页多少页'
		totalPages:'总页数'
		totalElements:'总数据量'
		data:[

{
id
  "date": "@Temporal(TemporalType.DATE) 操作日期",
  "projectId": "所属项目",
  "roomId": "所属房间",
  "projectName": "项目名称",
  "roomNo": "房间号",
  "optionType": "0 合闸 1 拉闸 3 清零",
  "balance": "操作时余额 (默认: 0)",
  "operator": "操作人"
}
			]
	}
}