1、获取物业详细信息
/sw/admin/wy/info/getOwnerInfo

get
id:物业账号id

响应:
{
	code:200,
	data:		{
	  "id": "公司ID",
	  "openKey": "大房东标志",
	  "companyName": "公司名称",
	  "responsiblePerson": "负责人/联系人姓名(对应数据库 responsible_person 字段)",
	  "responsibleTelephone": "负责人联系电话(对应数据库 responsible_telephone 字段)",
	  "createTime": "公司注册/创建时间(对应数据库 create_time 字段)",
	  "totalRooms": "总房间数(计算字段：所有关联的项目房间数)",
	  "rentedRooms": "已出租房间数(status = 1 的房间数量)",
	  "availableRooms": "待出租房间数(status = 0 的房间数量)",
	  "totalIncome": "收款总额(元)(已支付的租金账单总额，payment_status = 1 且 pay_type = 0)",
	  "totalWithdrawal": "提现总额(元)(已完成的提现申请总额，status = 2)",
	  "balance": "账户余额(元)(计算字段：totalIncome - totalWithdrawal) 注意：此字段需要业务逻辑计算，构造函数中不初始化"
	}
}