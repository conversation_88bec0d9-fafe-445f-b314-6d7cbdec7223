1、获取当前物业的线上资金情况
/sw/wy/withdrawal/apply/getWyAmount

GET
无参数
响应:
{
	code:200,
	message:null,
	data:{
  "totalAmount": "总收入（线上）",
  "eleAmount": "电费收入", 
  "rentAmount": "房租收入",
  "remainAmount": "剩余金额",
  "withdrawAmount": "已经提现金额"
}
}


2、获取提现列表

/sw/wy/withdrawal/apply/getApplyList
post:
content-type:application/json
参数:
{
	pageNo:页码
	pageSize:页大小
}

响应:
{
  code: 业务响应码 200 500...  
  message: 这个是异常的时候的消息提示
  data:{
    pageNo: '第几页'
    pageSize:'一页多少页'
    totalPages:'总页数'
    totalElements:'总数据量'
    data:[
 {
  "openKey": "物业标识符",
  "amount": "提现金额",
  "applyTime": "申请时间",
  "endTime": "结束时间",
  "status": "状态 (0=申请中, 1=已拒绝, 2=已成功)",
  "id": "申请id"
}]
  }
}


3、提交申请
/sw/wy/withdrawal/apply/apply
post:
content-type:application/json

参数:
{
	amount:提现金额
}

响应:
{
	code:200,
	message:null,
	data:null
}