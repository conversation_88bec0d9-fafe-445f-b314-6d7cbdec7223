<template>
  <div class="app-container">
    <!-- <div class="room-electricity-section dashboard-section">
      <div class="section-header">
        <i class="el-icon-lightning"></i>
        <span>查询房间用电量详情</span>
      </div> -->

      <div class="filter-container">
        <el-form
          :inline="true"
          :model="roomElectricityQuery"
          class="demo-form-inline"
        >
          <el-form-item label="项目名称">
            <el-select 
              v-model="roomElectricityQuery.projectId" 
              placeholder="请选择项目名称"
              clearable
            >
              <el-option 
                v-for="item in projectNameOptions" 
                :key="item.value" 
                :label="item.text" 
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="楼号">
            <el-input
              v-model="roomElectricityQuery.buildingNumber"
              placeholder="请输入楼号"
              clearable
              @keyup.enter.native="fetchRoomElectricityData"
            >
              <!-- <i slot="suffix" class="el-input__icon el-icon-search"></i> -->
            </el-input>
          </el-form-item>
          <el-form-item label="房间号">
            <el-input
              v-model="roomElectricityQuery.roomNo"
              placeholder="请输入房间号"
              clearable
              @keyup.enter.native="fetchRoomElectricityData"
            >
              <!-- <i slot="suffix" class="el-input__icon el-icon-search"></i> -->
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="fetchRoomElectricityData" icon="el-icon-search"
              >查询</el-button
            >
            <el-button @click="resetRoomQuery" icon="el-icon-refresh">重置</el-button>
            <el-button 
              v-if="roomElectricityList.length > 0" 
              type="warning" 
              @click="handleClearMeter(roomElectricityList[0])" 
              icon="el-icon-delete"
            >清零</el-button>
            <!-- <el-button 
              v-if="roomElectricityList.length > 0" 
              type="success" 
              @click="handleOpenAccount(roomElectricityList[0])" 
              icon="el-icon-key"
            >开户</el-button> -->
          </el-form-item>
        </el-form>
      </div>

      <!-- 房间基本电量使用信息 -->
      <el-card v-if="roomInfo.roomId" class="room-info-card" shadow="hover">
        <div class="room-info-header">
          <i class="el-icon-lightning"></i>
          <span>房间电量使用信息</span>
        </div>
        <div class="room-info-content">
          <div class="info-item">
            <span class="label">房间ID：</span>
            <span class="value">{{ roomInfo.roomId }}</span>
          </div>
          <div class="info-item">
            <span class="label">总用电量：</span>
            <span class="value">{{ roomInfo.totalUsed }} 度</span>
          </div>
          <div class="info-item">
            <span class="label">总电费：</span>
            <span class="value">{{ roomInfo.totalFee }} 元</span>
          </div>
        </div>
      </el-card>

      <el-table
        v-loading="roomElectricityLoading"
        :data="roomElectricityList"
        max-height="580"
        style="width: 100%"
        element-loading-text="Loading"
        border
        fit
        highlight-current-row
        class="main-table"
        @filter-change="handleRoomFilterChange"
      >
        <el-table-column label="日期" align="center" prop="date">
          <template #default="{ row }">{{
            parseTime(row.date, "YYYY-MM-DD")
          }}</template>
        </el-table-column>
        <el-table-column 
          label="项目名称" 
          align="center" 
          prop="projectName"
        />
        <el-table-column label="房间号" align="center" prop="roomNumber" />
        <el-table-column
          label="采集器号"
          align="center"
          prop="collectorNumber"
        />
        <el-table-column label="电表地址" align="center" prop="meterAddress" />
        <el-table-column label="状态" align="center" prop="status">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? "在线" : "离线" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="拉合闸状态" align="center" prop="switchStatus">
          <template #default="{ row }">
            <el-tag :type="row.switchStatus === 1 ? 'success' : 'warning'">
              {{ row.switchStatus === 1 ? "合闸" : "拉闸" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="用电量(度)"
          align="center"
          prop="electricityUsage"
        />
        <el-table-column
          label="用电金额"
          align="center"
          prop="electricityCost"
        />
        <el-table-column
          label="电费"
          align="center"
          prop="electricityAmount"
        >
          <template #default="{ row }">
            <span :style="{ color: row.electricityAmount > 0 ? '#F56C6C' : '' }">
              {{ row.electricityAmount }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="电费余额" align="center" prop="balance">
          <template #default="{ row }">
            <span :style="{ color: row.balance > 0 ? '#F56C6C' : '' }">
              {{ row.balance }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="清零" align="center" prop="clearNumber">
          <template #default="{ row }">
            <span :style="{ color: row.clearNumber > 0 ? '#E6A23C' : '' }">
              {{ row.clearNumber }}
            </span>
          </template>
        </el-table-column>
        <!-- <el-table-column label="开户" align="center" prop="openAccountNum">
          <template #default="{ row }">
            <span :style="{ color: row.openAccountNum > 0 ? '#67C23A' : '' }">
              {{ row.openAccountNum }}
            </span>
          </template>
        </el-table-column> -->
        <!-- <el-table-column label="操作" align="center" width="200">
          <template #default="{ row }">
            <el-button type="text" @click="handleViewRecharge(row)">
              充值记录
            </el-button>
            <el-button type="text" @click="handleViewClear(row)">
              清零记录
            </el-button>
            <template v-if="$index === 0">
                <el-button type="text" @click="handleViewOpenAccount(row)">
                  开户历史
                </el-button>
             </template>
          </template>
        </el-table-column> -->
      </el-table>

      <div class="pagination">
        <el-pagination
          @size-change="fetchRoomElectricityData"
          @current-change="fetchRoomElectricityData"
          :current-page.sync="roomElectricityQuery.pageNo"
          :page-sizes="[10, 20, 30, 50]"
          :page-size.sync="roomElectricityQuery.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="roomElectricityQuery.total"
        >
        </el-pagination>
      </div>
    <!-- </div> -->
    
    <!-- 充值记录弹窗 -->
    <el-dialog
      title="充值记录"
      :visible.sync="rechargeDialogVisible"
      width="70%"
      custom-class="history-dialog"
    >
      <el-table
        v-loading="rechargeLoading"
        :data="rechargeList"
        max-height="500"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="支付人" align="center" prop="payName" />
        <el-table-column label="支付类型" align="center" prop="type">
          <template #default="{ row }">
            <el-tag :type="getPayTypeTag(row.type)">
              {{ getPayTypeText(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="支付金额" align="center" prop="amount" />
        <el-table-column label="支付时间" align="center" prop="payTime">
          <template #default="{ row }">
            {{ parseTime(row.payTime, "YYYY-MM-DD HH:mm:ss") }}
          </template>
        </el-table-column>
        <el-table-column label="房间ID" align="center" prop="roomId" />
        <el-table-column label="支付方式" align="center" prop="payType">
          <template #default="{ row }">
            {{ row.payType === 0 ? "线下" : "线上" }}
          </template>
        </el-table-column>
        <el-table-column label="付款日" align="center" prop="billDate">
          <template #default="{ row }">
            {{ parseTime(row.billDate, "YYYY-MM-DD") }}
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination">
        <el-pagination
          @size-change="fetchRechargeData"
          @current-change="fetchRechargeData"
          :current-page.sync="rechargeQuery.pageNo"
          :page-sizes="[10, 20, 30, 50]"
          :page-size.sync="rechargeQuery.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="rechargeQuery.total"
        >
        </el-pagination>
      </div>
    </el-dialog>
    
    <!-- 清零记录弹窗 -->
    <el-dialog
      title="清零记录"
      :visible.sync="clearDialogVisible"
      width="70%"
      custom-class="history-dialog"
    >
      <el-table
        v-loading="clearLoading"
        :data="clearList"
        max-height="500"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="操作日期" align="center" prop="date">
          <template #default="{ row }">
            {{ parseTime(row.date, "YYYY-MM-DD") }}
          </template>
        </el-table-column>
        <el-table-column label="项目名称" align="center" prop="projectName" />
        <el-table-column label="房间号" align="center" prop="roomNo" />
        <el-table-column label="操作类型" align="center" prop="optionType">
          <template #default="{ row }">
            <el-tag :type="getOperationTypeTag(row.optionType)">
              {{ getOperationTypeText(row.optionType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作时余额" align="center" prop="balance" />
        <el-table-column label="操作人" align="center" prop="operator" />
      </el-table>
      
      <div class="pagination">
        <el-pagination
          @size-change="fetchClearData"
          @current-change="fetchClearData"
          :current-page.sync="clearQuery.pageNo"
          :page-sizes="[10, 20, 30, 50]"
          :page-size.sync="clearQuery.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="clearQuery.total"
        >
        </el-pagination>
      </div>
    </el-dialog>
    
    <!-- 开户历史弹窗 -->
    <!-- <el-dialog
      title="开户历史"
      :visible.sync="openAccountDialogVisible"
      width="70%"
      custom-class="history-dialog"
    >
      <el-table
        v-loading="openAccountLoading"
        :data="openAccountList"
        max-height="500"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="操作日期" align="center" prop="date">
          <template #default="{ row }">
            {{ parseTime(row.date, "YYYY-MM-DD") }}
          </template>
        </el-table-column>
        <el-table-column label="项目名称" align="center" prop="projectName" />
        <el-table-column label="房间号" align="center" prop="roomNo" />
        <el-table-column label="操作类型" align="center" prop="optionType">
          <template #default="{ row }">
            <el-tag :type="getOperationTypeTag(row.optionType)">
              {{ getOperationTypeText(row.optionType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作时余额" align="center" prop="balance" />
        <el-table-column label="操作人" align="center" prop="operator" />
      </el-table>
      
      <div class="pagination">
        <el-pagination
          @size-change="fetchOpenAccountData"
          @current-change="fetchOpenAccountData"
          :current-page.sync="openAccountQuery.pageNo"
          :page-sizes="[10, 20, 30, 50]"
          :page-size.sync="openAccountQuery.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="openAccountQuery.total"
        >
        </el-pagination>
      </div>
    </el-dialog> -->
  </div>
</template>

<script>
import * as api from "@/api/room";
import { getAllProject } from "@/api/project";
import { parseTime } from "@/utils";

export default {
  name: "RoomInquiry",
  data() {
    return {
      projectNameOptions: [], // 项目名称筛选选项
      // 房间用电量相关
      roomElectricityList: [],
      roomElectricityLoading: false,
      roomElectricityQuery: {
        projectId: "", // 默认为第一个项目的ID
        buildingNumber: "", // 楼号
        roomNo: "",
        pageNo: 1,
        pageSize: 50,
        total: 0,
      },
      // 房间基本电量使用信息
      roomInfo: {
        roomId: "",
        totalUsed: 0,
        totalFee: 0
      },
      // 充值记录相关
      rechargeDialogVisible: false,
      rechargeLoading: false,
      rechargeList: [],
      rechargeQuery: {
        reportId: "",
        pageNo: 1,
        pageSize: 50,
        total: 0,
      },
      // 清零记录相关
      clearDialogVisible: false,
      clearLoading: false,
      clearList: [],
      clearQuery: {
        reportId: "",
        pageNo: 1,
        pageSize: 50,
        total: 0,
      },
      // 开户历史相关
      // openAccountDialogVisible: false,
      // openAccountLoading: false,
      // openAccountList: [],
      // openAccountQuery: {
      //   reportId: "",
      //   pageNo: 1,
      //   pageSize: 50,
      //   total: 0,
      // },
    };
  },
  created() {
    this.getProjectList();
  },
  methods: {
    // 获取所有项目
    async getProjectList() {
      try {
        const { data } = await getAllProject();
        // 设置项目筛选选项
        this.projectNameOptions = data.map((item) => ({
          text: item.projectName,
          value: item.id,
        }));
        console.log("项目筛选选项:", this.projectNameOptions);

        // 设置默认项目ID为第一个项目
        // if (data && data.length > 0) {
        //   this.roomElectricityQuery.projectId = data[0].id;
        //   // 获取房间用电量数据
        //   // this.fetchRoomElectricityData();
        // }
      } catch (err) {
        console.error(err);
        this.$message.error("获取项目列表失败：" + (err.message || "未知错误"));
      }
    },
    
    // 获取房间用电量数据
    async fetchRoomElectricityData() {
      if (this.roomElectricityQuery.projectId == "") {
        this.$message.error("请选择项目名称");
        return;
      }
      if (this.roomElectricityQuery.roomNo == "") {
        this.$message.error("请输入房间号");
        return;
      }
      // if (this.roomElectricityQuery.buildingNumber == "") {
      //   this.$message.error("请输入楼号");
      //   return;
      // }
      try {
        this.roomElectricityLoading = true;
        
        // 获取房间基本电量使用信息
        await this.fetchRoomInfo();
        
        const params = {
          projectId: this.roomElectricityQuery.projectId,
          buildingNumber: this.roomElectricityQuery.buildingNumber,
          roomNo: this.roomElectricityQuery.roomNo,
          pageNo: this.roomElectricityQuery.pageNo,
          pageSize: this.roomElectricityQuery.pageSize,
        };
        
        const { data: res } = await api.getRoomElectricityUsage(params);
        const { data, pageNo, pageSize, totalElements } = res;
        this.roomElectricityList = data;
        this.roomElectricityQuery = {
          ...this.roomElectricityQuery,
          pageNo,
          pageSize,
          total: totalElements,
        };
      } catch (err) {
        this.roomElectricityList = [];
        this.rechargeQuery.pageNo = 1;
        console.error("获取房间用电量数据失败:", err);

      } finally {
        this.roomElectricityLoading = false;
      }
    },
    
    // 获取房间基本电量使用信息
    async fetchRoomInfo() {
      try {
        const params = {
          projectId: this.roomElectricityQuery.projectId,
          roomNo: this.roomElectricityQuery.roomNo,
          buildingNumber: this.roomElectricityQuery.buildingNumber
        };
        
        const { data } = await api.getRoomElectricityInfo(params);
        this.roomInfo = data;
      } catch (err) {
        console.error("获取房间基本电量使用信息失败:", err);
        this.roomInfo = {
          roomId: "",
          totalUsed: 0,
          totalFee: 0
        };
      }
    },
    
    // 处理房间用电量表格筛选变化
    handleRoomFilterChange(filters) {
      console.log("房间用电量表格筛选变化:", filters);
      
      // 处理项目名称筛选
      if ('projectName' in filters) {
        if (filters.projectName && filters.projectName.length > 0) {
          this.roomElectricityQuery.projectId = filters.projectName[0];
        } else {
          // 如果取消选中，则重置为第一个项目
          if (this.projectNameOptions && this.projectNameOptions.length > 0) {
            this.roomElectricityQuery.projectId = this.projectNameOptions[0].value;
          }
        }
      }
      
      // 重置到第一页并查询
      this.roomElectricityQuery.pageNo = 1;
      this.fetchRoomElectricityData();
    },

    // 重置房间查询条件
    resetRoomQuery() {
      this.roomElectricityQuery.roomNo = "";
      this.roomElectricityQuery.buildingNumber = "";
      this.roomElectricityQuery.projectId = "";
      this.roomElectricityQuery.pageNo = 1;
      this.roomElectricityList = [];
      this.roomInfo = {
        roomId: "",
        totalUsed: 0,
        totalFee: 0
      };
    },
    
    // 查看充值记录
    handleViewRecharge(row) {
      this.rechargeQuery.reportId = row.id;
      this.rechargeQuery.pageNo = 1;
      this.rechargeDialogVisible = true;
      this.fetchRechargeData();
    },
    
    // 获取充值记录数据
    async fetchRechargeData() {
      try {
        this.rechargeLoading = true;
        const params = {
          reportId: this.rechargeQuery.reportId,
          pageNo: this.rechargeQuery.pageNo,
          pageSize: this.rechargeQuery.pageSize,
        };

        const { data: res } = await api.getRechargeHistory(params);
        const { data, pageNo, pageSize, totalElements } = res;
        this.rechargeList = data;
        this.rechargeQuery = {
          ...this.rechargeQuery,
          pageNo,
          pageSize,
          total: totalElements,
        };
      } catch (err) {
        console.error(err);
        this.$message.error("获取充值记录失败：" + (err.message || "未知错误"));
      } finally {
        this.rechargeLoading = false;
      }
    },
    
    // 查看清零记录
    handleViewClear(row) {
      this.clearQuery.reportId = row.id;
      this.clearQuery.pageNo = 1;
      this.clearDialogVisible = true;
      this.fetchClearData();
    },
    
    // 获取清零记录数据
    async fetchClearData() {
      try {
        this.clearLoading = true;
        const params = {
          reportId: this.clearQuery.reportId,
          pageNo: this.clearQuery.pageNo,
          pageSize: this.clearQuery.pageSize,
        };

        const { data: res } = await api.getClearHistory(params);
        const { data, pageNo, pageSize, totalElements } = res;
        this.clearList = data;
        this.clearQuery = {
          ...this.clearQuery,
          pageNo,
          pageSize,
          total: totalElements,
        };
      } catch (err) {
        console.error(err);
        this.$message.error("获取清零记录失败：" + (err.message || "未知错误"));
      } finally {
        this.clearLoading = false;
      }
    },
    
    // 查看开户历史
    // handleViewOpenAccount(row) {
    //   this.openAccountQuery.reportId = row.id;
    //   this.openAccountQuery.pageNo = 1;
    //   this.openAccountDialogVisible = true;
    //   this.fetchOpenAccountData();
    // },
    
    // 获取开户历史数据
    // async fetchOpenAccountData() {
    //   try {
    //     this.openAccountLoading = true;
    //     const params = {
    //       reportId: this.openAccountQuery.reportId,
    //       pageNo: this.openAccountQuery.pageNo,
    //       pageSize: this.openAccountQuery.pageSize,
    //     };

    //     const { data: res } = await api.getOpenAccountHistory(params);
    //     const { data, pageNo, pageSize, totalElements } = res;
    //     this.openAccountList = data;
    //     this.openAccountQuery = {
    //       ...this.openAccountQuery,
    //       pageNo,
    //       pageSize,
    //       total: totalElements,
    //     };
    //   } catch (err) {
    //     console.error(err);
    //     this.$message.error("获取开户历史失败：" + (err.message || "未知错误"));
    //   } finally {
    //     this.openAccountLoading = false;
    //   }
    // },
    
    // 获取支付类型标签样式
    getPayTypeTag(type) {
      const tags = {
        0: 'success',  // 房租
        1: 'primary',  // 电费
        2: 'info'      // 水费
      };
      return tags[type] || 'info';
    },
    
    // 获取支付类型文本
    getPayTypeText(type) {
      const texts = {
        0: '房租',
        1: '电费',
        2: '水费'
      };
      return texts[type] || '未知';
    },
    
    // 获取操作类型文本
    getOperationTypeText(type) {
      const texts = {
        0: '合闸',
        1: '拉闸',
        3: '清零',
        4: '开户'
      };
      return texts[type] || '未知';
    },
    
    // 获取操作类型标签样式
    getOperationTypeTag(type) {
      const tags = {
        0: 'success',  // 合闸
        1: 'warning',  // 拉闸
        3: 'info',     // 清零
        4: 'primary'   // 开户
      };
      return tags[type] || 'info';
    },
    
    // 电表清零操作
    handleClearMeter(row) {
      this.$confirm('确认要对该房间电表进行清零操作吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await api.clearMeter(row.roomId);
          this.$message({
              type: 'success',
              message: '电表清零成功!'
            });
            // 刷新房间用电量数据
            this.fetchRoomElectricityData();
        } catch (error) {
          console.error('清零操作异常:', error);
          this.$message.error('电表清零失败: ' + (error.message || '未知错误'));
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消清零操作'
        });
      });
    },
    
    // 电表开户操作
    // handleOpenAccount(row) {
    //   this.$confirm('确认要对该房间电表进行开户操作吗?', '提示', {
    //     confirmButtonText: '确定',
    //     cancelButtonText: '取消',
    //     type: 'warning'
    //   }).then(async () => {
    //     try {
    //       await api.openAccount(row.roomId);
    //       this.$message({
    //         type: 'success',
    //         message: '电表开户成功!'
    //       });
    //       // 刷新房间用电量数据
    //       this.fetchRoomElectricityData();
    //     } catch (error) {
    //       console.error('开户操作异常:', error);
    //       this.$message.error('电表开户失败: ' + (error.message || '未知错误'));
    //     }
    //   }).catch(() => {
    //     this.$message({
    //       type: 'info',
    //       message: '已取消开户操作'
    //     });
    //   });
    // },
  },
};
</script>

<style scoped lang="scss">
// .dashboard-section {
//   background: #fff;
//   border-radius: 4px;
//   box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
//   padding: 20px;
//   margin-bottom: 20px;
// }

// .section-header {
//   display: flex;
//   align-items: center;
//   margin-bottom: 20px;
//   border-bottom: 1px solid #ebeef5;
//   padding-bottom: 15px;
  
//   i {
//     color: #409EFF;
//     font-size: 20px;
//     margin-right: 8px;
//   }
  
//   span {
//     font-size: 18px;
//     font-weight: 500;
//     color: #303133;
//   }
// }

// .room-electricity-section {
//   margin-top: 20px;
// }

.filter-container {
  margin-bottom: 15px;
  background: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
}

.pagination {
  padding-top: 20px;
  text-align: right;
}

.main-table {
  width: 100%;
  
  .el-button--text {
    color: #409EFF;
    font-weight: bold;
    
    &:hover {
      color: #66b1ff;
    }
  }
}

.history-dialog {
  .el-dialog__header {
    background: #409EFF;
    padding: 15px 20px;
    
    .el-dialog__title {
      color: white;
      font-weight: bold;
    }
  }
  
  .el-dialog__headerbtn .el-dialog__close {
    color: white;
  }
}

:deep(.el-tag) {
  padding: 0 10px;
  height: 26px;
  line-height: 24px;
}

.room-info-card {
  margin-bottom: 20px;
  
  .room-info-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    
    i {
      color: #409EFF;
      font-size: 20px;
      margin-right: 8px;
    }
    
    span {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }
  }
  
  .room-info-content {
    display: flex;
    flex-wrap: wrap;
    
    .info-item {
      margin-right: 30px;
      margin-bottom: 10px;
      
      .label {
        font-weight: bold;
        color: #606266;
      }
      
      .value {
        color: #409EFF;
        font-size: 16px;
      }
    }
  }
}
</style>