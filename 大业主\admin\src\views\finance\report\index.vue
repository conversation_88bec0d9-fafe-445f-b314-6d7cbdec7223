<!--
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-05-23 10:54:24
 * @LastEditors: wangxingyue
 * @LastEditTime: 2025-07-22 18:41:48
 * @FilePath: \ban-ban\大业主\admin\src\views\finance\report\index.vue
-->
<template>
  <div class="app-container">
    <!-- 导出 -->
    <!-- <div class="filter-container">
      <el-button type="primary" icon="el-icon-download" @click="handleExportByStream">导出Excel</el-button>
    </div> -->

    <el-table
      v-loading="listLoading"
      :data="list"
      max-height="580"
      style="width: 100%"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
      @filter-change="handleFilterChange"
      :default-filter="activeFilters"
    >
      <!-- <el-table-column label="所属物业" align="center" prop="openKey" /> -->
      <el-table-column label="统计日期" align="center" prop="reportDate">
        <template #default="{ row }">
          {{ parseTime(row.reportDate, "YYYY-MM-DD") }}
        </template>
      </el-table-column>
      <el-table-column
        label="项目名称"
        align="center"
        prop="projectName"
        :filters="projectNameOptions"
        filter-placement="bottom-end"
        :filter-multiple="false"
        column-key="projectName"
      />
      <el-table-column label="总房间数" align="center" prop="totalRooms" />
      <el-table-column label="已租房间数" align="center" prop="rentedRooms" />
      <el-table-column
        label="待租房间数"
        align="center"
        prop="availableRooms"
      />
      <el-table-column
        label="今日总收款"
        align="center"
        prop="todayTotalIncome"
      />
      <el-table-column
        label="电费收入"
        align="center"
        prop="electricityIncome"
      />
      <el-table-column label="房租收入" align="center" prop="rentIncome" />
      <el-table-column label="线上收款" align="center" prop="onlineIncome">
        <template #default="{ row }">
          <el-button type="text" @click="handleShowIncomeDetail(row, '1')">{{
            row.onlineIncome
          }}</el-button>
        </template>
      </el-table-column>
      <el-table-column label="线下收款" align="center" prop="offlineIncome">
        <template #default="{ row }">
          <el-button type="text" @click="handleShowIncomeDetail(row, '0')">{{
            row.offlineIncome
          }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination
        @size-change="fetchData"
        @current-change="fetchData"
        :current-page.sync="querySearch.pageNo"
        :page-sizes="[15, 30, 50, 100]"
        :page-size.sync="querySearch.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="querySearch.total"
      >
      </el-pagination>
    </div>

    <!-- 收款详情弹窗 -->
    <el-dialog
      title="收款详情"
      :visible.sync="detailDialogVisible"
      width="50%"
      :before-close="handleDialogClose"
    >
      <el-table
        v-loading="detailLoading"
        :data="detailList"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="项目名称" align="center" prop="projectName" />
        <el-table-column label="房间号" align="center" prop="roomNo" />
        <el-table-column label="金额" align="center" prop="amount" />
        <el-table-column label="用途" align="center" prop="type">
          <template #default="{ row }">
            {{ row.type == "0" ? "房租" : "电费" }}
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          @size-change="fetchDetailData"
          @current-change="fetchDetailData"
          :current-page.sync="detailQuery.pageNo"
          :page-sizes="[10, 20, 30, 50]"
          :page-size.sync="detailQuery.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="detailQuery.total"
        >
        </el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as api from "@/api/finance";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import { getAllProject } from "@/api/project";

export default {
  name: "MerchantBill",
  data() {
    return {
      list: null,
      listLoading: true,
      projectNameOptions: [], // 项目名称筛选选项
      activeFilters: {
        projectName: [], // 当前激活的筛选条件
      },
      querySearch: {
        pageNo: 1,
        pageSize: 50,
        total: 0,
        projectId: -1, // 默认查询全部项目
      },
      // 收款详情数据
      detailDialogVisible: false,
      detailLoading: false,
      detailList: [],
      detailQuery: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
        payType: "", // 0-线下 1-线上
        reportId: "", // 报表ID
      },
    };
  },
  mounted() {
    this.fetchData();
    this.getProjectList(); // 获取项目列表
  },
  activated() {
    this.fetchData();
  },
  methods: {
    async fetchData() {
      try {
        this.listLoading = true;

        // 构建查询参数
        const queryParams = {
          pageNo: this.querySearch.pageNo,
          pageSize: this.querySearch.pageSize,
          projectId: this.querySearch.projectId, // 始终传递projectId，即使是-1
        };

        // 同步更新activeFilters中的projectName
        if (this.querySearch.projectId !== -1) {
          this.activeFilters.projectName = [this.querySearch.projectId];
        } else {
          // 如果查询全部项目，清空projectName筛选
          this.activeFilters.projectName = [];
        }

        console.log("查询参数:", queryParams);

        const { data: res } = await api.findPropertyDailyReport(queryParams);
        const { data, pageNo, pageSize, totalElements } = res;
        this.list = data;
        this.querySearch = {
          ...this.querySearch,
          pageNo,
          pageSize,
          total: totalElements,
        };
      } catch (err) {
        console.error(err);
      } finally {
        this.listLoading = false;
      }
    },

    // 显示收款详情
    handleShowIncomeDetail(row, payType) {
      this.detailQuery = {
        pageNo: 1,
        pageSize: 10,
        total: 0,
        payType: payType, // 0-线下 1-线上
        reportId: row.id, // 假设报表ID存储在row.id中
      };
      this.detailDialogVisible = true;
      this.fetchDetailData();
    },

    // 关闭详情弹窗
    handleDialogClose() {
      this.detailDialogVisible = false;
      this.detailList = [];
    },

    // 获取收款详情数据
    async fetchDetailData() {
      try {
        this.detailLoading = true;
        const { data: res } = await api.findIncomeDetail(this.detailQuery);
        const { data, pageNo, pageSize, totalElements } = res;
        this.detailList = data;
        this.detailQuery = {
          ...this.detailQuery,
          pageNo,
          pageSize,
          total: totalElements,
        };
      } catch (err) {
        console.error(err);
        this.$message.error("获取收款详情失败：" + (err.message || "未知错误"));
      } finally {
        this.detailLoading = false;
      }
    },

    // 通过接口导出（模拟文件流）
    async handleExportByStream() {
      if (!this.list || this.list.length === 0) {
        this.$message.warning("没有数据可导出");
        return;
      }

      try {
        this.$message.info("正在导出数据，请稍候...");
        this.listLoading = true;

        // 模拟后端接口调用
        // 实际项目中可以直接调用 exportReportWithRealApi 方法
        // 当后端接口准备好后，可以取消注释下面这行，并删除模拟代码
        // return this.exportReportWithRealApi();

        // 模拟接口调用延迟
        await new Promise((resolve) => setTimeout(resolve, 1000));

        // 使用当前页数据生成Excel文件
        const exportData = this.list.map((item) => {
          return {
            统计日期: this.parseTime(item.reportDate, "YYYY-MM-DD"),
            总房间数: item.totalRooms,
            已租房间数: item.rentedRooms,
            待租房间数: item.availableRooms,
            今日总收款: item.todayTotalIncome,
            电费收入: item.electricityIncome,
            房租收入: item.rentIncome,
            线上收款: item.onlineIncome,
            线下收款: item.offlineIncome,
          };
        });

        // 创建工作簿
        const ws = XLSX.utils.json_to_sheet(exportData);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, "财务报表");

        // 设置列宽
        const wscols = [
          { wch: 15 }, // 统计日期
          { wch: 10 }, // 总房间数
          { wch: 10 }, // 已租房间数
          { wch: 10 }, // 待租房间数
          { wch: 12 }, // 今日总收款
          { wch: 10 }, // 电费收入
          { wch: 10 }, // 房租收入
          { wch: 10 }, // 线上收款
          { wch: 10 }, // 线下收款
        ];
        ws["!cols"] = wscols;

        // 生成文件流
        const wbout = XLSX.write(wb, {
          bookType: "xlsx",
          bookSST: true,
          type: "array",
        });

        // 模拟接口返回的文件流
        const fileBlob = new Blob([wbout], {
          type: "application/octet-stream",
        });

        // 创建下载链接并触发下载
        const url = URL.createObjectURL(fileBlob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `财务报表_${new Date().getTime()}.xlsx`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.$message.success("导出成功");
      } catch (err) {
        console.error(err);
        this.$message.error("导出失败：" + (err.message || "未知错误"));
      } finally {
        this.listLoading = false;
      }
    },

    // 真实项目中的接口调用示例（当后端接口准备好时可以使用）
    async exportReportWithRealApi() {
      try {
        this.listLoading = true;
        this.$message.info("正在请求数据，请稍候...");

        // 使用API函数导出数据
        const response = await api.exportPropertyDailyReport(this.querySearch);

        // 处理返回的文件流
        const blob = new Blob([response], { type: "application/octet-stream" });

        // 创建下载链接并触发下载
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `财务报表_${new Date().getTime()}.xlsx`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.$message.success("导出成功");
      } catch (err) {
        console.error(err);
        this.$message.error("导出失败：" + (err.message || "未知错误"));
      } finally {
        this.listLoading = false;
      }
    },

    // 获取项目列表
    async getProjectList() {
      try {
        const { data } = await getAllProject();
        // 设置项目筛选选项
        this.projectNameOptions = data.map((item) => ({
          text: item.projectName,
          value: item.id,
        }));
        console.log("项目筛选选项:", this.projectNameOptions);
      } catch (err) {
        console.error(err);
        this.$message.error("获取项目列表失败：" + (err.message || "未知错误"));
      }
    },

    // 处理筛选变化
    handleFilterChange(filters) {
      console.log("表格筛选变化:", filters);

      // 处理项目名称筛选
      if ("projectName" in filters) {
        // 更新activeFilters中的projectName
        this.activeFilters.projectName = filters.projectName || [];

        // 如果是项目名称筛选发生变化
        if (filters.projectName && filters.projectName.length > 0) {
          this.querySearch.projectId = filters.projectName[0];
        } else {
          this.querySearch.projectId = -1; // 如果取消选中，则重置为全部项目
        }
      }

      console.log("筛选条件:", {
        项目ID: this.querySearch.projectId,
      });

      // 重置到第一页
      this.querySearch.pageNo = 1;
      this.fetchData();
    },
  },
};
</script>

<style scoped lang="scss">
.filter-container {
  padding-bottom: 15px;
  display: flex;
  justify-content: flex-end;
}
.pagination {
  padding-top: 20px;
  text-align: right;
}
.drawer {
  padding: 0 20px;
}
</style>
