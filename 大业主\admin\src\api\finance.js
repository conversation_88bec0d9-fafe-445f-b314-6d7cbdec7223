/*
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-05-22 14:44:45
 * @LastEditors: wangxingyue
 * @LastEditTime: 2025-07-22 15:16:10
 * @FilePath: \ban-ban\大业主\admin\src\api\finance.js
 */
import request from "@/utils/request";

// 获取资金流水
export function getWyPayHistoryDetail(data) {
  return request({
    url: "/wy/withdrawal/apply/getWyPayHistoryDetail",
    method: "post",
    requestType: "json",
    data
  });
}

// 获取提现申请列表
export function getWithdrawalApplyList(data) {
  return request({
    url: "/wy/withdrawal/apply/getApplyList",
    method: "post",
    requestType: "json",
    data
  });
}

// 获取当前物业线上资金情况
export function getWyAmount() {
  return request({
    url: "/wy/withdrawal/apply/getWyAmount",
    method: "get"
  });
}

// 提交提现申请
export function submitWithdrawalApply(data) {
  return request({
    url: "/wy/withdrawal/apply/apply",
    method: "post",
    requestType: "json",
    data
  });
}

// 分页查询押金申请
export function getDepositOrders(data) {
  return request({
    url: "/wy/lease/info/getDepositOrders",
    method: "post",
    requestType: "json",
    data,
  });
}

// 批量通过
// /sw/wy/lease/info/passOrders
// get
// ids:1,2,3

// 响应:
// {
// 	code:200,
// 	data:null
// }
export function passOrders(params) {
  return request({
    url: "/wy/lease/info/passOrders",
    method: "get",
    // requestType: "form",
    params,
  });
}

// 批量拒绝
// /sw/wy/lease/info/rejectOrders
// get
// ids:1,2,3

// 响应:
// {
// 	code:200,
// 	data:null
// }
export function rejectOrders(params) {
  return request({
    url: "/wy/lease/info/rejectOrders",
    method: "get",
    // requestType: "form",
    params,
  });
}

// 报表列表
export function findPropertyDailyReport(data) {
  return request({
    url: "/wy/report/findPropertyDailyReport",
    method: "post",
    requestType: "json",
    data
  });
}

// 导出财务报表
export function exportPropertyDailyReport(data) {
  return request({
    url: "/wy/report/exportPropertyDailyReport",
    method: "post",
    requestType: "json",
    responseType: "blob", // 指定响应类型为blob
    data
  });
}

// 查询收款详情
export function findIncomeDetail(data) {
  return request({
    url: "/wy/report/findIncomeDetail",
    method: "post",
    requestType: "json",
    data
  });
}
