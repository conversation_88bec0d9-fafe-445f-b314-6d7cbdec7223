1、获取当前租赁关系
/sw/wy/lease/info/getLeaseInfo

GET
参数:
roomId:房屋id

响应:
{
	code:200,
	data:
	{
	id:唯一标识符
  "status": "客户类型(0=当前客户, 1=结束租赁客户)",
  "leaseStatus": "租赁状态(0=已租, 1=欠费, 2=已到期)",
  "startDate": "起租日期",
  "endDate": "到期日期",
  "monthlyRent": "月租金(元)",
  "tenantName": "承租人姓名",
  "phone": "联系电话",
  "industry": "所属行业",
  "remark": "备注信息(已租/待租/终止状态)",
  "roomId": "房间id",
  "deposit": "押金(元)",
  "paymentType": "付款方式(0=年付,1=半年付,2=季付,3=月付,4=日付)",
  "totalAmount": "总租金",
  "payedAmount": "已付租金"
}
}


2、终止租赁关系
/sw/wy/lease/info/stopLease

post:
content-type:application/json

参数:
{
	id:id,
	remark:'备注'
}
响应:
{
	code:200
	message:null
	data:null
}


3、添加租赁人员信息
/sw/wy/lease/info/addLeaseInfo
post:
content-type:application/json

参数:

{
  "leaseStatus": "租赁状态(0=已租, 1=合同期内欠费, 2=合同已经到期)",
  "startDate": "起租日期",
  "endDate": "到期日期",
  "monthlyRent": "月租金(元)",
  "tenantName": "承租人姓名",
  "phone": "联系电话",
  "industry": "所属行业",
  "remark": "备注信息(已租/待租/到期终止/其他终止)",
  "roomId": "房间id",
  "deposit": "押金(元)",
  "paymentType": "付款方式(0=年付, 1=半年付, 2=季付, 3=月付, 4=日付)"
}



响应:
{
	code:200,
	message:null,
	data:null
}


4、修改租赁人员信息
/sw/wy/lease/info/modifyLeaseInfo
post:
content-type:application/json

参数:
{
  "id": "租赁id",
  "leaseStatus": "租赁状态(0=已租, 1=合同期内欠费, 2=合同已经到期)",
  "startDate": "起租日期",
  "endDate": "到期日期",
  "monthlyRent": "月租金(元)",
  "tenantName": "承租人姓名",
  "phone": "联系电话",
  "industry": "所属行业",
  "remark": "备注信息(已租/待租/到期终止/其他终止)",
  "roomId": "房间id",
  "deposit": "押金(元)",
  "paymentType": "付款方式(0=年付, 1=半年付, 2=季付, 3=月付, 4=日付)"
}
响应:
{
	code:200,
	message:null,
	data:null
}

5、获取房屋租赁历史列表
/sw/wy/lease/info/getLeaseHistory
post:
content-type:application/json
{
	pageNo:页码,
	pageSize:页大小,
	roomId:房屋id
}

响应:
{
  code: 业务响应码 200 500...  
  message: 这个是异常的时候的消息提示
  data:{
    pageNo: '第几页'
    pageSize:'一页多少页'
    totalPages:'总页数'
    totalElements:'总数据量'
    data:[
  {
  "status": "客户类型(0=当前客户, 1=结束租赁客户)",
  "leaseStatus": "租赁状态(0=已租, 1=欠费, 2=已到期)",
  "startDate": "起租日期",
  "endDate": "到期日期",
  "monthlyRent": "月租金(元)",
  "tenantName": "承租人姓名",
  "phone": "联系电话",
  "industry": "所属行业",
  "remark": "备注信息(已租/待租/终止状态)",
  "roomId": "房间id",
  "deposit": "押金(元)",
  "paymentType": "付款方式(0=年付,1=半年付,2=季付,3=月付,4=日付)",
  "totalAmount": "总租金",
  "payedAmount": "已付租金"
}]
  }
}