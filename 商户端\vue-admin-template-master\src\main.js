/*
 *Author: CBB
 *Date: 2025-03-16 21:35:03
 *LastEditTime: 2025-03-18 00:45:21
 *LastEditors: CBB
 *Description:
 *FilePath: \安小页\商户端\vue-admin-template-master\src\main.js
 */
import Vue from "vue";

import "normalize.css/normalize.css"; // A modern alternative to CSS resets

import ElementUI from "element-ui";
import "element-ui/lib/theme-chalk/index.css";
// import locale from 'element-ui/lib/locale/lang/en' // lang i18n

import "@/styles/index.scss"; // global css

import App from "./App";
import store from "./store";
import router from "./router";

import "@/icons"; // icon
import "@/permission"; // permission control

// set ElementUI lang to EN
Vue.use(ElementUI);
// 如果想要中文版 element-ui，按如下方式声明
// Vue.use(ElementUI)

Vue.config.productionTip = false;

new Vue({
  el: "#app",
  router,
  store,
  render: (h) => h(App),
});
