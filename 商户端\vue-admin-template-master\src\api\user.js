/*
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-04-03 11:26:44
 * @LastEditors: wangxingyue
 * @LastEditTime: 2025-05-26 16:04:39
 * @FilePath: \ban-ban\商户端\vue-admin-template-master\src\api\user.js
 */
/*
 *Author: CBB
 *Date: 2025-03-16 21:35:03
 *LastEditTime: 2025-03-17 00:13:33
 *LastEditors: CBB
 *Description:
 *FilePath: \vue-admin-template-master\src\api\user.js
 */
import request from "@/utils/request";

export function getSmsCode(telephone) {
  return request({
    url: "/auth/getPhone",
    method: "post",
    data: {
      telephone,
    },
  });
}

/**
 *
 * telephone=13516440569 必填
code = 123456 必填
checked = 1   隐私协议是否选中  必填 0 未选中 1选中

@returns  
 "token": "Bearer 1901175636886687744", //token
 "status": 3 //账户状态
 */
export function login(data) {
  return request({
    url: "/auth/login",
    method: "post",
    data,
  });
}

export function logout() {
  return request({
    url: "/auth/logout",
    method: "get",
  });
}
