1、获取基础类型与类型列表
/sw/admin/service/type/getAllBaseTypeList

get

无参数

响应:{
	code:200,
	message:null,
	data:
	[
		{
		  "baseId": "基础类型ID",
		  "baseName": "基础类型名称",
		  "type": "额外的类别信息 排序或者其他",
		  "serviceTypeItemVoList": [
		    {
		      "id": "id",
		      "serviceTypeName": "服务类型名称",
		      "description": "描述 一般主页有",
		      "iconUrl": "主页图片",
		      "imageUrl": "进入页面图片",
		      "homeSort": "主页排序",
		      "pageSort": "进入页面排序",
		      "baseTypeId": "基础类型id"
		    },
		    {
		      "id": "id",
		      "serviceTypeName": "服务类型名称",
		      "description": "描述 一般主页有",
		      "iconUrl": "主页图片",
		      "imageUrl": "进入页面图片",
		      "homeSort": "主页排序",
		      "pageSort": "进入页面排序",
		      "baseTypeId": "基础类型id"
		    }
		  ]
		}


	]

	
}


2、添加基础类型
/sw/admin/service/type/addBaseType
POST:
content-teype:application/json
参数:
{
  "baseTypeName": "基础类型名称",
  "type": "额外信息"
}
响应:{
	code:200,
	message:null,
	data:null
}





3、修改基础类型
/sw/admin/service/type/modifyBaseType
POST:
content-teype:application/json
参数:
{
  "id": "ID",
  "baseTypeName": "基础类型名称",
  "type": "额外信息"
}
响应:{
	code:200,
	message:null,
	data:null
}

4、添加商户类型
/sw/admin/service/type/addServiceType
POST:
content-teype:application/json
参数:
{
  "serviceTypeName": "服务名称",
  "description": "服务描述",
  "iconUrl": "类别图片",
  "imageUrl": "主页图片",
  "homeSort": "主页排序",
  "pageSort": "类别列表排序",
  "baseTypeId": "父级id"
}
响应:{
	code:200,
	message:null,
	data:null
}

5、修改商户类型
/sw/admin/service/type/modifyServiceType
POST:
content-teype:application/json
参数:
{
  "id": "ID",
  "serviceTypeName": "服务名称",
  "description": "服务描述",
  "iconUrl": "类别图片",
  "imageUrl": "主页图片",
  "homeSort": "主页排序",
  "pageSort": "类别列表排序",
  "baseTypeId": "父级id"
}
响应:{
	code:200,
	message:null,
	data:null
}


6、删除服务类型
/sw/admin/service/type/removeServiceType
get
参数id

响应:
{
	code:200,
	message:null,
	data:null
}
