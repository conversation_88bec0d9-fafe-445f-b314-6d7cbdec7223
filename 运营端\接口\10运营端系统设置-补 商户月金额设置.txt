## 基础信息
- 基础路径：/sw/admin/system/config
- 认证方式：Bearer <PERSON>ken
- 返回格式：JSON

## 数据结构

### SystemConfig 对象
字段名 | 类型 | 说明
------|------|------
id | Long | 主键ID，对应配置键名
template | String | 配置项的值

### ResultDto 通用返回
{
  "code": 200,
  "message": "success",
  "data": {}
}

商户月金额获取
- URL: /getShPayMoney
- Method: GET
- 描述: 获取商户月金额金额配置
- 响应示例:
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "SH_SPRING_MONEY_KEY",
    "template": "0.1"
  }
}


### 8. 更新系统配置
- URL: /setSystemConfig
- Method: POST 
- content-type:application/json
- 请求参数:
{
  "id": "配置键名",
  "template": "新值"
}
- 请求示例:
{
  "id": "1",
  "template": ""
}
- 成功响应:
{
  "code": 200,
  "message": "success",
  "data": null
}
