1、获取商户资料
/sw/sh/shop/getShopInfo

GET
无参数

响应:
{
	code:200,
	message:null,
	data:{
  "shopName": "店铺名称",
  "mainBusiness": "主营业务",
  "description": "店铺简介",
  "address": "店铺地址",
  "imageUrl": "图片URL列表",
  "contactPerson": "联系人姓名",
  "phone": "联系电话",
  "businessLicenseUrl": "营业执照图片URL",
  "openKey": "商户标识",
  "paymentTime": "付费日",
  "expiredTime": "到期日",
  "totalAmount": "总付费",
  "status": "状态  1 在线  0 下线"
}
}


2、设置商户资料
/sw/sh/shop/setShopInfo

post
content-type:application/json

参数:
{
  "shopName": "店铺名称",
  "mainBusiness": "主营业务",
  "description": "店铺简介",
  "address": "店铺地址",
  "imageUrl": "图片URL列表",
  "contactPerson": "联系人姓名",
  "phone": "联系电话",
  "businessLicenseUrl": "营业执照图片URL",
  "openKey": "商户标识",
  "status": "状态  1 在线  0 下线"
}


响应:
{
	code:200,
	message:null,
	data:null
}
