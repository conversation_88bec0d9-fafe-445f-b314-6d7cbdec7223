<template>
  <div class="app-container">
    <!-- 使用el-table组件展示商户资料 -->
    <div class="shop-info-card" v-if="shopInfo">
      <h4 class="info-title">商户资料</h4>
      <el-table :data="shopInfoTable" border style="width: 100%" :show-header="false">
        <el-table-column prop="label" width="180"></el-table-column>
        <el-table-column prop="value"></el-table-column>
      </el-table>
    </div>

    <div class="addBtn">
        <el-button type="primary" @click="Recharge()"
          >充值</el-button
        >
      </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="加载中..."
      border
      fit
      highlight-current-row
    >
      <el-table-column align="center" type="index" width="80" />
      <el-table-column label="充值时间" align="center" prop="paymentTime" />
      <el-table-column label="充值金额" align="center" prop="paymentAmount" />
      <el-table-column label="余额" align="center" prop="remainAmount" />
    </el-table>
    <div class="pagination">
      <el-pagination
        @size-change="fetchData"
        @current-change="fetchData"
        :current-page.sync="querySearch.pageNo"
        :page-sizes="[15, 30, 50, 100]"
        :page-size.sync="querySearch.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="querySearch.total"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { getPaymentOrder } from "@/api/table";
import { getShopInfo } from "@/api/userInfo";

export default {
  data() {
    return {
      list: null,
      listLoading: true,
      shopInfo: null, // 商户资料
      querySearch: {
        pageNo: 1,
        pageSize: 50,
        total: 0,
      },
    };
  },
  computed: {
    // 将商户信息转换为表格数据格式
    shopInfoTable() {
      if (!this.shopInfo) return [];
      return [
        { label: "ID", value: this.shopInfo.openKey || '0' },
        { label: "店铺名称", value: this.shopInfo.shopName || '暂无' },
        { label: "状态", value: this.shopInfo.status === 1 ? '在线' : '下线', type: this.shopInfo.status === 1 ? 'success' : 'danger' },
        { label: "付费日", value: this.shopInfo.paymentTime || '暂无' },
        { label: "到期日", value: this.shopInfo.expiredTime || '暂无' },
        { label: "总付费", value: this.shopInfo.totalAmount || '0' }
      ];
    }
  },
  created() {
    this.getShopInfoData(); // 获取商户资料
    this.fetchData();
  },
  methods: {
    async fetchData() {
      try {
        this.listLoading = true;
        const { data: res } = await getPaymentOrder(this.querySearch);
        console.log(res);
        const { data, pageNo, pageSize, totalElements } = res;
        this.list = data;
        this.querySearch = {
          ...this.querySearch,
          pageNo,
          pageSize,
          total: totalElements,
        };
      } catch (err) {
        console.error(err);
      } finally {
        this.listLoading = false;
      }
    },
    Recharge(){
        this.$message({
          type: 'info',
          message: '该功能还未开发'
        });  
        return
    },
    // 获取商户资料
    async getShopInfoData() {
      try {
        const { data: res } = await getShopInfo();
        console.log('获取商户资料',res);
        this.shopInfo = res;
      } catch (err) {
        console.error('获取商户资料失败', err);
        this.$message.error('获取商户资料失败');
      }
    }
  },
};
</script>

<style scoped lang="scss">
.pagination {
  padding-top: 20px;
  text-align: right;
}

.addBtn {
  padding-bottom: 20px;
}

.shop-info-card {
  margin-bottom: 20px;
}

.info-title {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: bold;
}
</style>
