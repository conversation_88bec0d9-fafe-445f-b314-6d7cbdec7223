1、房间导入
/sw/wy/project/room/importProjectRoom

post:
file:
projectId

响应:
{
	code:200,
	data:null
}


2、房间查询
/sw/wy/project/room/getProjectRoom

post:
{
  "pageNo": 1,          // 页码（当前第1页）
  "pageSize": 10,       // 页大小（每页10条）
  "projectId": 1001,    // 所属项目ID（示例项目ID -1 全部）
  "roomNo": "A-101",    // 房屋编号（物理编号）
  "status": 1           // 状态（1表示待租 0 已租 -1 全部）
}

响应:
{
  code: 业务响应码 200 500...  
  message: 这个是异常的时候的消息提示
  data:{
    pageNo: '第几页'
    pageSize:'一页多少页'
    totalPages:'总页数'
    totalElements:'总数据量'
    data:[
  {
  id:数据id
  "openKey": "物业唯一标识",
  "projectName": "项目名称",
  "buildingNumber": "楼号",
  "roomNumber": "房间号",
  "area": "面积(平方米)",
  "electricityMeterNumber": "电表号",
  "status": "状态(如: 空闲/已租)",
  "price": "报价(元/月)",
  "imageUrl": "房间图片URL",
  "videoUrl": "房间视频URL"
}]
  }
}

