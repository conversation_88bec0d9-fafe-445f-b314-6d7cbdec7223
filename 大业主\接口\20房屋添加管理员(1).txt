1、获取房屋管理员列表

/sw/wy/room/manager/getAllManager

get

响应:
{
	code:200,
	data:[{
		id:id
	  "name": "张三",          // 租户姓名（示例值）
	  "telephone": "13800138000",  // 联系电话（示例值）
	}]
}

2、添加管理员
/sw/wy/room/manager/addRoomManager

post

{
	  "roomId": 1001,          // 所属房间ID（示例值）
	  "name": "张三",          // 租户姓名（示例值）
	  "telephone": "13800138000",  // 联系电话（示例值）
	  leaseInfoId：合同id
	}

响应:
{
	code:200,
	data:null
}


3、删除管理员
/sw/wy/room/manager/removeRoomManager

GET
managerId

响应:
{
	code:200,
	data:null
}