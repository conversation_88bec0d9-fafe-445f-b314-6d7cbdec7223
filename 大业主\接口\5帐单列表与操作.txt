1、当前房屋当前的基本数据
/sw/wy/lease/info/getRoomAmount

GET
参数:
roomId:房屋id

响应:
{
	code:200
	message:null
	data:{
  "totalAmount": "总租费",
  "remainingAmount": "剩余金额",
  "paidAmount": "已付金额"
}
}

2、获取订单列表
/sw/wy/rent/bill/getRentBillsByPage
post:
content-type:application/json
{
	pageNo:页码,
	pageSize:页大小,
	roomId:房屋id
}


响应:
{
  code: 业务响应码 200 500...  
  message: 这个是异常的时候的消息提示
  data:{
    pageNo: '第几页'
    pageSize:'一页多少页'
    totalPages:'总页数'
    totalElements:'总数据量'
    data:[
  {
  "paymentType": "支付方式 (0=年付, 1=半年付, 2=季付, 3=月付, 4=日付)",
  "current": "当前账单 (0=否, 1=是)",
  "paymentStatus": "支付状态 (0=未支付, 1=已支付)",
  "operator": "操作人 (记录操作的管理员或系统)",
  "operationTime": "操作时间",
  "startDate": "账单开始日期 (租金覆盖的开始日期)",
  "endDate": "账单结束日期 (租金覆盖的结束日期)",
  "rentAmount": "租金金额 (使用BigDecimal处理金额精度)",
  "billNumber": "账单编号 (可选，建议添加)",
  "roomId": "关联的租赁合同ID (可选，建议添加)"
}]
  }
}


3、支付订单

/sw/wy/rent/bill/payRentBill
post:
content-type:application/json

参数:
{
  "id": "唯一标识",
  "operator": "操作人"
}

响应:
{
	code:200,
	message:null,
	data:null
}