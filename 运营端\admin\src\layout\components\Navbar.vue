<template>
  <div class="navbar">
    <!-- <hamburger :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" /> -->
    <breadcrumb class="breadcrumb-container" />

    <div class="right-menu">
      <el-dropdown class="avatar-container" trigger="click">
        <div class="avatar-wrapper">
          <img src="../../assets/img/logo.png" class="user-avatar">
          <i class="el-icon-caret-bottom" />
        </div>
        <el-dropdown-menu slot="dropdown" class="user-dropdown">
          <router-link to="/">
            <el-dropdown-item> 首页 </el-dropdown-item>
          </router-link>
          <el-dropdown-item divided @click.native="showPasswordDialog">
            <span style="display: block">修改密码</span>
          </el-dropdown-item>
          <el-dropdown-item divided @click.native="logout">
            <span style="display: block">退出登录</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>

    <!-- 修改密码对话框 -->
    <el-dialog 
      title="修改密码" 
      :visible.sync="passwordDialogVisible" 
      width="30%" 
      custom-class="password-dialog"
      :modal-append-to-body="false"
      :append-to-body="true"
    >
      <el-form :model="passwordForm" :rules="passwordRules" ref="passwordForm" label-width="100px">
        <el-form-item label="旧密码" prop="oldPassword">
          <el-input type="password" v-model="passwordForm.oldPassword" placeholder="请输入旧密码"></el-input>
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input type="password" v-model="passwordForm.newPassword" placeholder="请输入新密码"></el-input>
        </el-form-item>
        <el-form-item label="确认新密码" prop="confirmPassword">
          <el-input type="password" v-model="passwordForm.confirmPassword" placeholder="请再次输入新密码"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="passwordDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="modifyPassword" :loading="submitLoading">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import Breadcrumb from "@/components/Breadcrumb";
import Hamburger from "@/components/Hamburger";
import * as api from "@/api/user";

export default {
  components: {
    Breadcrumb,
    Hamburger,
  },
  data() {
    // 确认密码验证
    const validateConfirmPassword = (rule, value, callback) => {
      if (value !== this.passwordForm.newPassword) {
        callback(new Error('两次输入的密码不一致'));
      } else {
        callback();
      }
    };
    return {
      passwordDialogVisible: false,
      submitLoading: false,
      passwordForm: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      passwordRules: {
        oldPassword: [
          { required: true, message: '请输入旧密码', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请再次输入新密码', trigger: 'blur' },
          { validator: validateConfirmPassword, trigger: 'blur' }
        ]
      }
    };
  },
  computed: {
    ...mapGetters(["sidebar", "userInfo", "statusObj"]),
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch("app/toggleSideBar");
    },
    showPasswordDialog() {
      this.passwordDialogVisible = true;
      this.passwordForm = {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      };
      // 清除表单验证状态
      this.$nextTick(() => {
        if (this.$refs.passwordForm) {
          this.$refs.passwordForm.clearValidate();
        }
      });
    },
    modifyPassword() {
      this.$refs.passwordForm.validate(async (valid) => {
        if (valid) {
          try {
            this.submitLoading = true;
            await api.modifyPassword(this.passwordForm);
            this.$message.success('密码修改成功');
            this.passwordDialogVisible = false;
            // 重置表单
            this.passwordForm = {
              oldPassword: '',
              newPassword: '',
              confirmPassword: ''
            };
            this.logout()
          } catch (error) {
            console.error('密码修改失败:', error);
            this.$message.error('密码修改失败，请检查旧密码是否正确');
          } finally {
            this.submitLoading = false;
          }
        } else {
          return false;
        }
      });
    },
    async logout() {
      await this.$store.dispatch("user/logout");
      this.$router.push(`/login?redirect=${this.$route.fullPath}`);
    },
  },
};
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      margin-right: 30px;

      p {
        padding-right: 10px;
      }

      .avatar-wrapper {
        margin-top: 5px;
        position: relative;
        display: flex;
        align-items: center;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          font-size: 12px;
        }
      }
    }
  }
}
</style>

<!-- 全局样式，修复对话框层级问题 -->
<style lang="scss">
// 修复密码对话框的z-index层级问题
.password-dialog {
  // 确保对话框内容在最上层
  z-index: 3000 !important;
  
  // 对话框内容样式
  .el-dialog {
    position: relative;
    z-index: 3001 !important;
  }
  
  // 表单样式优化
  .el-form {
    .el-form-item {
      margin-bottom: 22px;
    }
    
    .el-input {
      width: 100%;
    }
  }
  
  // 按钮样式
  .dialog-footer {
    text-align: right;
    
    .el-button {
      margin-left: 10px;
    }
  }
}

// 修复对话框遮罩层的z-index
.el-dialog__wrapper {
  &:has(.password-dialog) {
    z-index: 2999 !important;
  }
}

// 兼容性处理：如果浏览器不支持:has选择器，使用属性选择器
.el-dialog__wrapper[aria-label*="修改密码"] {
  z-index: 2999 !important;
  
  .el-dialog__header {
    background-color: #f5f7fa;
    border-bottom: 1px solid #e4e7ed;
  }
  
  .el-dialog__title {
    font-weight: 600;
    color: #303133;
  }
}

// 确保遮罩层在对话框内容下方
.el-overlay {
  z-index: 2998 !important;
}

// 修复可能的滚动条问题
.el-popup-parent--hidden {
  .navbar {
    padding-right: 15px;
  }
}
</style>