<template>
  <div class="">
    <!-- 房屋信息展示 -->
    <div style="margin-bottom: 10px">
      <el-descriptions
        title="房屋信息"
        :column="3"
        border
        v-loading="roomInfoLoading"
      >
        <el-descriptions-item label="项目名称">{{
          roomInfo.projectName
        }}</el-descriptions-item>
        <el-descriptions-item label="楼号">{{
          roomInfo.buildingNumber
        }}</el-descriptions-item>
        <el-descriptions-item label="房间号">{{
          roomInfo.roomNumber
        }}</el-descriptions-item>
        <!-- <el-descriptions-item label="面积(平方米)">{{
          roomInfo.area
        }}</el-descriptions-item>
        <el-descriptions-item label="电表号">{{
          roomInfo.electricityMeterNumber
        }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="roomInfo.status == '0' ? 'success' : 'info'">
            {{ roomInfo.status == '0' ? '已租' : '待租' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="报价(元/月)">{{
          roomInfo.price
        }}</el-descriptions-item>
        <el-descriptions-item label="房间图片">
          <el-image 
            v-if="roomInfo.imageUrl" 
            :src="roomInfo.imageUrl" 
            style="width: 100px; height: 100px;"
            :preview-src-list="[roomInfo.imageUrl]">
          </el-image>
          <span v-else>暂无图片</span>
        </el-descriptions-item>
        <el-descriptions-item label="房间视频">
          <video 
            v-if="roomInfo.videoUrl" 
            :src="roomInfo.videoUrl" 
            controls 
            style="width: 200px; max-height: 150px;">
            您的浏览器不支持视频标签
          </video>
          <span v-else>暂无视频</span>
        </el-descriptions-item> -->
      </el-descriptions>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      max-height="580"
      style="width: 100%"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
      <el-table-column label="id" align="center" prop="id" />
      <el-table-column label="项目名称" align="center" prop="projectName" />
      <el-table-column label="所属房屋" align="center" prop="roomId" />
      <el-table-column label="修改时间" align="center" prop="updateTime">
        <template #default="{ row }">{{
          parseTime(row.updateTime, "YYYY-MM-DD")
        }}</template>
      </el-table-column>
      <el-table-column label="楼号" align="center" prop="buildingNumber" />
      <el-table-column label="房间号" align="center" prop="roomNumber" />
      <el-table-column label="面积(平方米)" align="center" prop="area" />
      <el-table-column
        label="电表号"
        align="center"
        prop="electricityMeterNumber"
      />
      <el-table-column label="状态" align="center" prop="status">
        <!-- 状态(如: 空闲/已租) 0 已租 1 待租 -->
        <template #default="{ row }">
          <el-tag :type="statusFilter(row.status).type">{{
            statusFilter(row.status).name
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="报价(元/月)" align="center" prop="price" />
      <el-table-column label="房间图片" align="center" prop="imageUrl">
        <template #default="{ row }">
          <el-image
            v-if="row.imageUrl"
            style="width: 50px; height: 50px"
            :src="row.imageUrl"
            fit="contain"
            :preview-src-list="[row.imageUrl]"
          />
          <span v-else>暂无图片</span>
        </template>
      </el-table-column>
      <el-table-column label="房间视频" align="center">
        <template #default="{ row }">
          <el-button
            v-if="row.videoUrl"
            type="text"
            @click="previewVideo(row.videoUrl)"
          >
            查看视频
          </el-button>
          <span v-else>暂无视频</span>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination">
      <el-pagination
        @size-change="fetchData"
        @current-change="fetchData"
        :current-page.sync="querySearch.pageNo"
        :page-sizes="[15, 30, 50, 100]"
        :page-size.sync="querySearch.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="querySearch.total"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import * as api from "@/api/room";
export default {
  name: "Onlineretailerslist",
  data() {
    return {
      list: null,
      listLoading: true,
      querySearch: {
        pageNo: 1,
        pageSize: 50,
        total: 0,
      },

      statusOptions: [
        {
          name: "已租",
          value: "0",
          type: "success",
        },
        {
          name: "待租",
          value: "1",
          type: "info",
        },
      ],

      roomInfo: {
        id: "",
        openKey: "",
        projectName: "",
        buildingNumber: "",
        roomNumber: "",
        area: "",
        electricityMeterNumber: "",
        status: "",
        price: "",
        imageUrl: "",
        videoUrl: "",
      },
      roomInfoLoading: true,
    };
  },
  computed: {
    roomId() {
      return this.$route.params.id || "";
    },
  },
  created() {
    this.fetchData();
    this.fetchRoomInfo();
  },
  methods: {
    statusFilter(status) {
      return (
        this.statusOptions.find((e) => e.value == status) || {
          name: "未知",
          value: "",
          type: "",
        }
      );
    },

    async fetchData() {
      try {
        this.listLoading = true;
        const { data: res } = await api.getRoomHistories({
          ...this.querySearch,
          roomId: this.roomId,
        });
        const { data, pageNo, pageSize, totalElements } = res;
        this.list = data;
        this.querySearch = {
          pageNo,
          pageSize,
          total: totalElements,
        };
      } catch (err) {
        console.error(err);
      } finally {
        this.listLoading = false;
      }
    },

    previewVideo(url) {
      window.open(url, "_blank");
    },

    async fetchRoomInfo() {
      try {
        this.roomInfoLoading = true;
        const res = await api.getProjectRoomInfo(this.roomId);
        console.log(res);
        if (res.code === 200) {
          this.roomInfo = res.data || {};
        } else {
          this.$message.error(res.message || "获取房屋信息失败");
          this.roomInfo = {};
        }
      } catch (err) {
        console.error(err);
        this.$message.error("获取房屋信息失败");
        this.roomInfo = {};
      } finally {
        this.roomInfoLoading = false;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.pagination {
  padding-top: 20px;
  text-align: right;
}
::v-deep .el-descriptions__header {
  margin-bottom: 10px !important;
}
</style>