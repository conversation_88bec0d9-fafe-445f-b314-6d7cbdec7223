<template>
  <div class="wrap" v-loading="pageLoading">
    <div class="form">
      <el-form
        ref="form"
        :model="form"
        label-width="150px"
        size="small"
        :rules="rules"
        :disabled="!isEdit || submitLoading"
      >
        <!-- <div class="formTitle">业主资料</div> -->
        <!-- <el-form-item label="ID：" prop="id" v-if="form.id">
          <el-tag size="mini" effect="plain">{{ form.id }}</el-tag>
        </el-form-item> -->

        <!-- <el-form-item label="状态">
          <el-tag size="mini" :type="statusObj.type" effect="dark">{{
            statusObj.text
          }}</el-tag>
        </el-form-item> -->

        

        <el-form-item label="公司：" prop="companyName">
          <el-input
            v-model="form.companyName"
            placeholder="请输入公司"
          ></el-input>
        </el-form-item>

        <el-form-item label="电话" prop="phone">
          <el-input
            type="number"
            v-model="form.phone"
            placeholder="请输入电话"
          ></el-input>
        </el-form-item>

        <div class="flex">
          <el-form-item label="地址：" prop="address">
            <el-input
              v-model="form.address"
              type="textarea"
              placeholder="请输入详细地址"
              autosize
            ></el-input>
          </el-form-item>
        </div>

        <el-form-item label="营业执照：" prop="businessLicenseUrl">
          <el-upload
            :class="{ hide: hideUpload }"
            class="avatar-uploader"
            :action="`${BASE_URL}/axy/file/uploadFile`"
            :on-success="handleSuccess"
            :limit="1"
            :before-upload="handleUpload"
            accept="image/jpeg,image/png,image/jpg"
            with-credentials
            :multiple="false"
            :file-list="fileList"
            list-type="picture-card"
            :on-remove="
              () => {
                form.businessLicenseUrl = '';
                fileList = [];
              }
            "
          >
            <i class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
          <span v-show="!hideUpload" class="uploadTips">
            仅支持jpg、png、jpeg格式的图片，且单张大小不能超过10M</span
          >
        </el-form-item>
        <!-- <el-form-item label="房东头像：" prop="imageUrl">
          <el-upload
            :class="{ hide: hideimageUrlUpload }"
            class="avatar-uploader"
            :action="`${BASE_URL}/axy/file/uploadFile`"
            :on-success="handleimageUrlSuccess"
            :limit="1"
            :before-upload="handleImageUpload"
            accept="image/jpeg,image/png,image/jpg"
            with-credentials
            :multiple="false"
            :file-list="imageUrlFileList"
            list-type="picture-card"
            :on-remove="
              () => {
                form.imageUrl = '';
                imageUrlFileList = [];
              }
            "
          >
            <i class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
          <span v-show="!hideimageUrlUpload" class="uploadTips">
            仅支持jpg、png、jpeg格式的图片，且单张大小不能超过10M</span
          >
        </el-form-item> -->
        <div class="flex">
          <el-form-item label="负责人：" prop="responsiblePerson">
            <el-input
              v-model="form.responsiblePerson"
              placeholder="请输入负责人"
            ></el-input>
          </el-form-item>
          <el-form-item label-width="0" prop="responsibleTelephone">
            <el-input
              v-model="form.responsibleTelephone"
              placeholder="请输入负责人电话"
            ></el-input>
          </el-form-item>
        </div>

        <div class="flex">
          <el-form-item label="账户信息：" prop="bankName">
            <el-input
              v-model="form.bankName"
              placeholder="请输入开户行"
            ></el-input>
          </el-form-item>
          <el-form-item label-width="0" prop="bankAccountName">
            <el-input
              v-model="form.bankAccountName"
              placeholder="请输入账户名"
            ></el-input>
          </el-form-item>
          <el-form-item label-width="0" prop="bankAccount">
            <el-input
              v-model="form.bankAccount"
              placeholder="请输入银行账号"
            ></el-input>
          </el-form-item>
        </div>

        <el-form-item label="合同：" prop="contractUrl">
          <div style="display: flex; align-items: center">
            <el-upload
              :class="{ hide: hideContractUpload }"
              class="contract-uploader"
              :action="`${BASE_URL}/axy/file/uploadFile`"
              :on-success="handleContractSuccess"
              :on-progress="handleContractProgress"
              :on-error="handleContractError"
              :limit="1"
              :before-upload="handleContractUpload"
              accept=".pdf,.doc,.docx"
              with-credentials
              :multiple="false"
              :file-list="contractFileList"
              :show-file-list="true"
            >
              <el-button
                size="small"
                type="primary"
                :disabled="!isEdit || submitLoading"
                icon="el-icon-upload2"
              >
                上传合同
              </el-button>
            </el-upload>
            <div v-if="form.contractUrl" class="contract-info">
              <el-link
                type="primary"
                :href="form.contractUrl"
                target="_blank"
                :underline="false"
              >
                <i class="el-icon-document"></i> 查看合同
              </el-link>
              <el-button
                v-if="isEdit"
                type="text"
                size="small"
                @click="form.contractUrl = ''"
                class="delete-btn"
              >
                <i class="el-icon-delete"></i> 删除
              </el-button>
            </div>
            <span v-show="!hideContractUpload" class="uploadTips">
              仅支持pdf、doc、docx格式的文件，且单文件大小不能超过10M</span
            >
          </div>
        </el-form-item>
      </el-form>
      <div class="btnBox">
        <el-button
          v-if="isEdit"
          type="primary"
          @click="onSubmit()"
          :loading="submitLoading"
          >提交</el-button
        >
        <el-button v-else type="warning" @click="onEdit()">修改</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import * as api from "@/api/owner";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      BASE_URL: process.env.VUE_APP_SERVER_URL,
      fileList: [],
      imageUrlFileList: [],
      contractFileList: [],
      uploadProgress: 0,
      rules: {
        companyName: [
          { required: true, message: "请输入公司名称", trigger: "blur" },
        ],
        phone: [{ required: true, message: "请输入联系电话", trigger: "blur" }],
        address: [
          { required: true, message: "请输入详细地址", trigger: "blur" },
        ],
        // imageUrl: [
        //   { required: true, message: "请上传房东头像", trigger: "change" },
        // ],
        businessLicenseUrl: [
          { required: true, message: "请上传营业执照", trigger: "change" },
        ],
        responsiblePerson: [
          { required: true, message: "请输入负责人", trigger: "blur" },
        ],
        responsibleTelephone: [
          { required: true, message: "请输入负责人电话", trigger: "blur" },
        ],
        bankName: [
          { required: true, message: "请输入开户行", trigger: "blur" },
        ],
        bankAccountName: [
          { required: true, message: "请输入账户名", trigger: "blur" },
        ],
        bankAccount: [
          { required: true, message: "请输入银行账号", trigger: "blur" },
        ],
        // contractUrl: [
        //   { required: true, message: "请上传合同", trigger: "false" },
        // ],
      },
      isEdit: false,
      submitLoading: false,
      pageLoading: false,
      form: {
        id: "",
        companyName: "",
        phone: "",
        address: "",
        // imageUrl: "",
        businessLicenseUrl: "",
        responsiblePerson: "",
        responsibleTelephone: "",
        bankName: "",
        bankAccountName: "",
        bankAccount: "",
        contractUrl: "",
        remark: "",
      },
    };
  },
  created() {
    this.getOwnerInfo();
  },
  computed: {
    ...mapGetters(["userInfo", "statusObj"]),
    hideUpload() {
      return this.fileList.length >= 1 || this.form.businessLicenseUrl;
    },
    hideContractUpload() {
      return this.contractFileList.length >= 1 || this.form.contractUrl;
    },
    hideimageUrlUpload() {
      return this.imageUrlFileList.length >= 1 || this.form.imageUrl;
    },
  },
  methods: {
    async getOwnerInfo() {
      try {
        this.pageLoading = true;
        const { data } = await api.getOwnerInfo();
        console.log(data);
        this.form = data;
        if (data.businessLicenseUrl) {
          this.fileList = [
            {
              status: "success",
              percentage: 100,
              url: data.businessLicenseUrl,
              response: {
                code: 200,
                data: data.businessLicenseUrl,
              },
            },
          ];
          
          // if (data.imageUrl) {
          //   this.imageUrlFileList = [
          //     {
          //       status: "success",
          //       percentage: 100,
          //       url: data.imageUrl,
          //       response: {
          //         code: 200,
          //         data: data.imageUrl,
          //       },
          //     },
          //   ];
          // }
          
          this.form = {
            ...data,
          };
          this.isEdit = false;
        } else {
          this.isEdit = true;
          this.form = {
            id: "",
            companyName: "",
            phone: "",
            address: "",
            // imageUrl: "",
            businessLicenseUrl: "",
            responsiblePerson: "",
            responsibleTelephone: "",
            bankName: "",
            bankAccountName: "",
            bankAccount: "",
            contractUrl: "",
            remark: "",
          };
          this.fileList = [];
          this.imageUrlFileList = [];
        }
      } catch (err) {
        console.error(err);
      } finally {
        this.pageLoading = false;
      }
    },

    handleSuccess(response, file, fileList) {
      console.log("handleSuccess==>", response, file, fileList);
      const { code, data, message } = response;
      if (code == 200) {
        this.form.businessLicenseUrl = data;
        console.log(this.form.businessLicenseUrl);
      } else {
        this.$message.error(message || "上传失败");
      }
    },
    handleUpload(file) {
      console.log("handleUpload==>", file);
      const isJPG = ["image/jpg", "image/png", "image/jpeg"].includes(
        file.type
      );
      const isLt2M = file.size / 1024 / 1024 < 10;

      if (!isJPG) {
        return this.$message.error("上传图片只能是 JPG/PNG/JPEG 格式!");
      }
      if (!isLt2M) {
        return this.$message.error("上传图片大小不能超过 10MB!");
      }
    },
    handleContractSuccess(response, file, fileList) {
      console.log("handleContractSuccess==>", response, file, fileList);
      const { code, data, message } = response;
      if (code == 200) {
        this.form.contractUrl = data;
        this.$message({
          type: "success",
          message: "合同上传成功",
        });
        console.log(this.form.contractUrl);
      } else {
        this.$message.error(message || "合同上传失败");
        // 上传失败时移除文件
        this.contractFileList = [];
      }
    },
    handleContractUpload(file) {
      console.log("handleContractUpload==>", file);
      const isPDF = file.type === "application/pdf";
      const isDOC = [
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      ].includes(file.type);
      const isLt10M = file.size / 1024 / 1024 < 10;

      if (!isPDF && !isDOC) {
        this.$message.error("上传文件只能是 PDF/DOC/DOCX 格式!");
        return false;
      }
      if (!isLt10M) {
        this.$message.error("上传文件大小不能超过 10MB!");
        return false;
      }
      // // 开始上传时显示提示
      // this.$message({
      //   type: 'info',
      //   message: '开始上传合同文件...'
      // });
      return true;
    },
    handleContractError(err, file, fileList) {
      console.error("handleContractError==>", err, file, fileList);
      this.$message.error(err || "合同上传失败，请重试");
      this.contractFileList = [];
    },
    handleContractProgress(event, file, fileList) {
      this.uploadProgress = Math.round(event.percent);
    },
    // handleimageUrlSuccess(response, file, fileList) {
    //   console.log("handleimageUrlSuccess==>", response, file, fileList);
    //   const { code, data, message } = response;
    //   if (code == 200) {
    //     this.form.imageUrl = data;
    //     this.imageUrlFileList = fileList;
    //     console.log(this.form.imageUrl);
    //   } else {
    //     this.$message.error(message || "头像上传失败");
    //   }
    // },
    // handleImageUpload(file) {
    //   console.log("handleImageUpload==>", file);
    //   const isJPG = ["image/jpg", "image/png", "image/jpeg"].includes(
    //     file.type
    //   );
    //   const isLt2M = file.size / 1024 / 1024 < 10;

    //   if (!isJPG) {
    //     return this.$message.error("上传图片只能是 JPG/PNG/JPEG 格式!");
    //   }
    //   if (!isLt2M) {
    //     return this.$message.error("上传图片大小不能超过 10MB!");
    //   }
    // },
    onEdit() {
      this.isEdit = true;
      window.scrollTo({
        top: 0,
        behavior: "smooth",
      });
    },
    async onSubmit() {
      try {
        await this.$refs.form.validate();
        const postData = {
          // openKey: this.userInfo.openKey,
          companyName: this.form.companyName,
          phone: this.form.phone,
          address: this.form.address,
          imageUrl: this.form.imageUrl,
          businessLicenseUrl: this.form.businessLicenseUrl,
          responsiblePerson: this.form.responsiblePerson,
          responsibleTelephone: this.form.responsibleTelephone,
          bankName: this.form.bankName,
          bankAccountName: this.form.bankAccountName,
          bankAccount: this.form.bankAccount,
          contractUrl: this.form.contractUrl,
        };

        this.submitLoading = true;
        const { code, message } = await api.setOwnerInfo(postData);
        if (code === 200) {
          this.$message({
            type: "success",
            message: "提交成功",
          });
          this.getOwnerInfo();
        } else {
          this.$message.error(message || "提交失败");
        }
      } catch (err) {
        console.error(err);
        if (err.message) {
          this.$message.error(err.message);
        }
      } finally {
        this.submitLoading = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.wrap {
  .title {
    font-size: 30px;
    line-height: 1.5;
    font-weight: 500;
    padding-bottom: 10px;
  }

  .form {
    width: 800px;

    .formTitle {
      font-size: 18px;
      line-height: 1.5;
      padding: 20px 0;
      font-weight: 600;
    }

    .uploadTips {
      color: #666;
      font-size: 12px;
      margin-left: 20px;
    }

    .preview {
      position: relative;
      width: 100px;
      height: 100px;

      .model {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
      }

      img {
        display: block;
        object-fit: contain;
        width: 100%;
        height: 100%;
      }

      .el-icon-delete {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        cursor: pointer;
        color: #fff;
      }
    }
  }
}

::v-deep .hide .el-upload--picture-card {
  display: none;
}

.fileLink {
  padding: 0 10px;
  color: #409eff;
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}

::v-deep .el-cascader {
  width: 100% !important;
}

.btnBox {
  padding-left: 150px;
}

.flex {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;

  > div {
    flex: 1;
  }
}

.contract-uploader {
  display: inline-block;

  ::v-deep .el-upload-list {
    margin-top: 10px;

    .el-upload-list__item {
      .el-progress {
        margin-top: 5px;
      }
    }
  }
}

.contract-info {
  // margin-top: 10px;
  margin-left: 20px;
  display: flex;
  align-items: center;
  gap: 10px;

  .delete-btn {
    color: #f56c6c;
    padding: 0;

    &:hover {
      color: #f78989;
    }
  }
}

.uploadTips {
  display: block;
  margin-top: 5px;
  color: #666;
  font-size: 12px;
}

.flex {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;

  > div {
    flex: 1;
  }
}
</style>
