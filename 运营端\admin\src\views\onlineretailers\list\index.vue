<!--
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-05-27 10:49:49
 * @LastEditors: wangxingyue
 * @LastEditTime: 2025-07-25 14:38:48
 * @FilePath: \ban-ban\运营端\admin\src\views\onlineretailers\list\index.vue
-->
<template>
  <div class="app-container">
    <el-form inline>
      <el-form-item label="订单编号：">
        <el-input
          v-model="querySearch.no"
          placeholder="请输入订单编号"
          clearable
        />
      </el-form-item>
      &nbsp;
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="fetchData()"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetSearch()"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <el-table
      v-loading="listLoading"
      :data="list"
      max-height="580"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
      <el-table-column label="序号" align="center" type="index" width="100" />
      <el-table-column label="手机号" align="center" prop="telephone" />
      <el-table-column label="归属业主" align="center" prop="ownerName" />
      <el-table-column label="下单时间" align="center" prop="createTime">
        <template #default="{ row }">{{
          parseTime(row.createTime, "YYYY-MM-DD HH:mm:ss")
        }}</template>
      </el-table-column>
      <el-table-column label="支付额" align="center">
        <template #default="{ row }">
          {{
            row.actualCosPrice > 0 ? row.actualCosPrice : row.estimateCosPrice
          }}
        </template>
      </el-table-column>
      <el-table-column
        label="预估佣金"
        align="center"
        prop="estimateTotalMoney"
      />
      <el-table-column label="订单状态" align="center" prop="status">
        <!-- 订单状态：1=待付款，2=已付款，3=已完成，4=失效 -->
        <template #default="{ row }">
          <el-tag :type="statusFilter(row.status).type">{{
            statusFilter(row.status).name
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="平台" align="center" prop="channelId">
        <!-- 渠道 ID：1=淘宝,2=京东,3=拼多多,4=美团 -->
        <template #default="{ row }">
          <span :style="{ color: channelIdFilter(row.channelId).color }">{{
            channelIdFilter(row.channelId).name
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="商品图片" align="center" prop="goodsImage">
        <template #default="{ row }">
          <el-image
            style="width: 50px; height: 50px"
            :src="row.goodsImage"
            fit="contain"
            :preview-src-list="[row.goodsImage]"
          />
        </template>
      </el-table-column>
      <el-table-column label="商品名称" align="center" prop="goodsTitle" />
    </el-table>

    <div class="pagination">
      <el-pagination
        @size-change="fetchData"
        @current-change="fetchData"
        :current-page.sync="querySearch.pageNo"
        :page-sizes="[15, 30, 50, 100]"
        :page-size.sync="querySearch.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="querySearch.total"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import * as api from "@/api/onlineretailers";
export default {
  name: "Onlineretailerslist",
  data() {
    return {
      list: null,
      listLoading: true,
      querySearch: {
        pageNo: 1,
        pageSize: 50,
        no: "",
        total: 0,
      },

      statusOptions: [
        {
          name: "全部",
          value: "-1",
          type: "success",
        },
        {
          name: "待付款",
          value: "1",
          type: "info",
        },
        {
          name: "已付款",
          value: "2",
          type: "",
        },
        {
          name: "已完成",
          value: "3",
          type: "success",
        },
        {
          name: "失效",
          value: "4",
          type: "info",
        },
      ],

      channelIdOptions: [
        {
          name: "淘宝",
          value: "1",
          color: "#FF5000",
        },
        {
          name: "京东",
          value: "2",
          color: "#E3221D",
        },
        {
          name: "拼多多",
          value: "3",
          color: "#E12A1F",
        },
        {
          name: "美团",
          value: "4",
          color: "#FFD100",
        },
      ],
    };
  },
  created() {
    this.fetchData();
  },
  methods: {
    statusFilter(status) {
      return (
        this.statusOptions.find((e) => e.value == status) || {
          name: "未知",
          value: "",
          type: "",
        }
      );
    },

    channelIdFilter(channelId) {
      return (
        this.channelIdOptions.find((e) => e.value == channelId) || {
          name: "未知",
          value: "",
          type: "",
        }
      );
    },

    async fetchData() {
      try {
        this.listLoading = true;
        const { data: res } = await api.getAllOrder(this.querySearch);
        const { data, pageNo, pageSize, totalElements } = res;
        this.list = data;
        this.querySearch = {
          pageNo,
          pageSize,
          total: totalElements,
          no: this.querySearch.no,
        };
      } catch (err) {
        console.error(err);
      } finally {
        this.listLoading = false;
      }
    },

    // 重置搜索条件
    resetSearch() {
      this.querySearch = {
        pageNo: 1,
        pageSize: 50,
        no: "",
        total: 0,
      };
      // 重置到第一页并获取数据
      this.querySearch.pageNo = 1;
      this.fetchData();
    },
  },
};
</script>

<style scoped lang="scss">
.pagination {
  padding-top: 20px;
  text-align: right;
}
</style>