1、通知列表
/sw/wy/advice/getAdviceByPage
GET
参数:
pageNo:页码
pageSize:页大小
响应:
响应:
{
	code: 业务响应码 200 500...  
	message: 这个是异常的时候的消息提示
	data:{
		pageNo: '第几页'
		pageSize:'一页多少页'
		totalPages:'总页数'
		totalElements:'总数据量'
		data:[
		{
  "id": "id",
  "title": "标题",
  "content": "内容",
  "publishTime": "发布时间",
  "publisher": "发布者",
}]
	}
}


2、添加通知
/sw/wy/advice/addAdvice
post
content-type:application/json
参数:
{
  "title": "标题",
  "content": "内容",
  "publisher": "发布者"
}
响应:
{
	code:200,
	data:null,
	message:null
}


3、通过id获取通知
/sw/wy/advice/getAdviceById
get
参数:
id:

响应:
{
	code:200,
	data:	{
  "id": "id",
  "title": "标题",
  "content": "内容",
  "publishTime": "发布时间",
  "publisher": "发布者",
}
}



4、修改通知
/sw/wy/advice/modifyAdvice
post
content-type:application/json
参数:
{
"id":"id",
  "title": "标题",
  "content": "内容",
  "publisher": "发布者"
}
响应:
{
	code:200,
	data:null,
	message:null
}



5、删除通知
/sw/wy/advice/removeAdvice

get
参数:
id:

响应:
{
	code:200,
	data:null,
	message:null
}