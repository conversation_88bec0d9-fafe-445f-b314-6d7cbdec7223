1、获取提现申请列表
/sw/admin/withdrawal/getWithdrawalOrders
post:
cotent-type:application/json

参数:
{
	pageNo:1
	pageSize:10
	status:0 待审核  1 审核通过 2 审核失败 3 打款中 4 提现成功 5 打款失败 其他一切值都是全部
	
}

响应:
{
	code: 业务响应码 200 500...  
	message: 这个是异常的时候的消息提示
	data:{
		pageNo: '第几页'
		pageSize:'一页多少页'
		totalPages:'总页数'
		totalElements:'总数据量'
		data:[
	{
  "orderNo": "订单号",
  "points": "扣除积分",
  "status": "0 待审核  1 审核通过 2 审核失败 3 打款中 4 提现成功 5 打款失败",
  "applyTime": "申请日期",
  "money": "提现金额",
  "avatar": "头像",
  "nickName": "昵称"
  id:id
}
		]
	}
}


2、批量通过
/sw/admin/withdrawal/passOrders

GET
ids:1,2,3
响应:
{
	code:200,
	data:null
}

3、批量拒绝
/sw/admin/withdrawal/rejectOrders
GET
ids:1,2,3
响应:
{
	code:200,
	data:null
}