1、支付订单

/sw/wy/rent/bill/payRentBills  
post:
content-type:application/json

参数:
{
  "billIds":[1,2,3] //账单id列表
  "operator": "操作人"
}

响应:
{
	code:200,
	message:null,
	data:null
}

2、获取订单列表
/sw/wy/rent/bill/getRentBillsByPage
post:
content-type:application/json
{
	pageNo:页码,
	pageSize:页大小,
	roomId:房屋id,
	 paymentStatus; //支付状态 0 未支付 1 已支付 -1 取消 2 or null 全部
	payType;//支付方式 0 线下 1 线上  -1 未支付  2 全部
}


响应:
{
  code: 业务响应码 200 500...  
  message: 这个是异常的时候的消息提示
  data:{
    pageNo: '第几页'
    pageSize:'一页多少页'
    totalPages:'总页数'
    totalElements:'总数据量'
    data:[
  {
  "paymentType": "支付方式 (0=年付, 1=半年付, 2=季付, 3=月付, 4=日付)",
  "current": "当前账单 (0=否, 1=是)",
  "paymentStatus": "支付状态 (0=未支付, 1=已支付)",
  "operator": "操作人 (记录操作的管理员或系统)",
  "operationTime": "操作时间",
  "startDate": "账单开始日期 (租金覆盖的开始日期)",
  "endDate": "账单结束日期 (租金覆盖的结束日期)",
  "rentAmount": "租金金额 (使用BigDecimal处理金额精度)",
  "billNumber": "账单编号 (可选，建议添加)",
  "roomId": "关联的租赁合同ID (可选，建议添加)"
}]
  }
}

3、获取房屋列表

/sw/wy/project/room/getProjectRoom

post:
content-type:application/json
参数:
{
  pageNo: 页码
  pageSize:页大小
  roomNo:房屋号
}

响应:
{
  code: 业务响应码 200 500...  
  message: 这个是异常的时候的消息提示
  data:{
    pageNo: '第几页'
    pageSize:'一页多少页'
    totalPages:'总页数'
    totalElements:'总数据量'
    data:[
  {
  id:数据id
  "openKey": "物业唯一标识",
  "projectName": "项目名称",
  "buildingNumber": "楼号",
  "roomNumber": "房间号",
  "area": "面积(平方米)",
  "electricityMeterNumber": "电表号",
  "status": "状态(如: 空闲/已租)",
  "price": "报价(元/月)",
  "imageUrl": "房间图片URL",
  "videoUrl": "房间视频URL"
}]
  }
}

4、根据id获取房屋信息
/sw/wy/project/room/getProjectRoomInfo
GET

roomId:你懂的

响应:
{
  code:200,
  data:{
    id:数据id
    "openKey": "物业唯一标识",
    "projectName": "项目名称",
    "buildingNumber": "楼号",
    "roomNumber": "房间号",
    "area": "面积(平方米)",
    "electricityMeterNumber": "电表号",
    "status": "状态(如: 空闲/已租)",
    "price": "报价(元/月)",
    "imageUrl": "房间图片URL",
    "videoUrl": "房间视频URL"
  }
}