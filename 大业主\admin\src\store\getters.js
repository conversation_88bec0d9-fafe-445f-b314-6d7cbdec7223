/*
 *Author: CBB
 *Date: 2025-03-18 19:00:23
 *LastEditTime: 2025-04-01 20:42:29
 *LastEditors: CBB
 *Description:
 *FilePath: \admin\src\store\getters.js
 */
import EnumStep from "@/plugins/stepEnum";
import EnumStatus from "@/plugins/statusEnum";

const getters = {
  sidebar: (state) => state.app.sidebar,
  device: (state) => state.app.device,
  token: (state) => state.user.token,
  userInfo: (state) => state.user.userInfo,
  stepPage: (state) => {
    const { step } = state.user.userInfo;
    if (step !== null) {
      return {
        ...EnumStep[step],
      };
    }
    return null;
  },
  statusObj: (state) => {
    const { status, step } = state.user.userInfo;
    if (status == null)
      return {
        text: "未知",
        type: "info",
      };
    const statusObj = {
      ...EnumStatus[status],
    };
    // if (step !== null) {
    //   const stepPage = EnumStep[step];
    //   if (stepPage?.title) {
    //     console.log(" statusObj.text==>", statusObj.text);

    //     statusObj.text = statusObj.text + `（${stepPage?.title}）`;
    //   }
    // }
    return statusObj;
  },
};
export default getters;
