<template>
  <div>
    <!-- 房屋租金信息 -->
    <div class="report-data">
      <div class="rent-info-container">
        <el-card class="rent-info-card" shadow="hover">
          <div slot="header" class="card-header">
            <i class="el-icon-money"></i>
            <span>房屋租金信息</span>
          </div>

          <!-- 房屋信息展示 -->
          <div class="room-info-section">
            <el-descriptions
              :column="3"
              border
              size="small"
              v-loading="roomInfoLoading"
            >
              <el-descriptions-item label="项目名称">{{
                roomInfo.projectName
              }}</el-descriptions-item>
              <el-descriptions-item label="楼号">{{
                roomInfo.buildingNumber
              }}</el-descriptions-item>
              <el-descriptions-item label="房间号">{{
                roomInfo.roomNumber
              }}</el-descriptions-item>
            </el-descriptions>
          </div>

          <div class="amount-row">
            <div class="amount-item">
              <div class="amount-label">总租费</div>
              <div class="amount-value total">
                {{ roomAmount.totalAmount || 0 }} <span class="unit">元</span>
              </div>
            </div>
            <div class="amount-item">
              <div class="amount-label">已付金额</div>
              <div class="amount-value paid">
                {{ roomAmount.paidAmount || 0 }} <span class="unit">元</span>
              </div>
            </div>
            <div class="amount-item">
              <div class="amount-label">剩余金额</div>
              <div class="amount-value remaining">
                {{ roomAmount.remainingAmount || 0 }}
                <span class="unit">元</span>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 查询表单 -->
    <div class="filter-container">
      <el-form :inline="true" :model="queryParams" class="form-inline">
        <el-form-item label="状态：">
          <el-select
            v-model="queryParams.paymentStatus"
            placeholder="请选择状态"
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="(item, index) in statusOptions"
              :key="index + item.value"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        &nbsp;
        <el-form-item>
          <el-button type="primary" @click="fetchData()">搜索</el-button>
          <el-button
            :disabled="!multipleSelection.length"
            type="success"
            @click="handlePay(multipleSelection)"
            >批量支付</el-button
          >
        </el-form-item>
      </el-form>
    </div>

    <el-table
      v-loading="loading"
      :data="billList"
      max-height="580"
      style="width: 100%"
      element-loading-text="加载中"
      border
      fit
      highlight-current-row
      :row-class-name="tableRowClassName"
      ref="multipleTable"
      @selection-change="handleSelectionChange"
      :row-key="(_) => _.id"
    >
      <el-table-column
        type="selection"
        width="55"
        align="center"
        reserve-selection
      />
      <el-table-column label="账单日期" align="center" prop="startDate">
        <template #default="{ row }">
          {{ parseTime(row.startDate, "YYYY-MM-DD") }} 至
          {{ parseTime(row.endDate, "YYYY-MM-DD") }}
        </template>
      </el-table-column>
      <el-table-column label="账单编号" align="center" prop="billNumber" />
      <el-table-column label="租金金额" align="center" prop="rentAmount" />

      <el-table-column label="支付方式" align="center">
        <template #default="{ row }">
          {{ getPaymentTypeText(row.paymentType).name }}
        </template>
      </el-table-column>
      <el-table-column label="支付状态" align="center">
        <template #default="{ row }">
          <el-tag :type="statusFilter(row.paymentStatus).type">{{
            statusFilter(row.paymentStatus).name
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作人" align="center" prop="operator" />
      <el-table-column label="操作时间" align="center" prop="payTime">
        <template #default="{ row }">
          {{ parseTime(row.payTime, "YYYY-MM-DD HH:mm:ss") }}
        </template>
      </el-table-column>
      <!-- <el-table-column label="操作" align="center" width="100">
        <template #default="{ row }">
          <el-button
            type="text"
            size="mini"
            @click="handlePay(row)"
            v-if="row.paymentStatus == 0"
            >支付</el-button
          >
          <span v-else-if="row.paymentStatus == 1" class="text-muted"
            >已支付</span
          >
        </template>
      </el-table-column> -->
      <el-table-column label="操作" align="center">
        <template #default="{ row }">
          <el-button
            v-if="row.paymentStatus == 0"
            type="text"
            @click="handlePay([row.id])"
            >支付</el-button
          >
          <span v-else-if="row.paymentStatus == 1" class="text-muted">
            {{
              row.payType == 0
                ? "已支付（线下）"
                : row.payType == 1
                ? "已支付（线上）"
                : "已支付"
            }}
          </span>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParams.pageNo"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="queryParams.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      />
    </div>
  </div>
</template>
    
<script>
import {
  getRentBillsByPage,
  payRentBill,
  getRoomAmount,
  getProjectRoomInfo,
} from "@/api/room";
export default {
  data() {
    return {
      loading: true,
      amountLoading: false,
      roomInfoLoading: false,
      roomInfo: {},
      billList: [],
      total: 0,
      queryParams: {
        pageNo: 1,
        pageSize: 50,
        roomId: "",
        paymentStatus: null,
        payType: "2",
      },
      multipleSelection: [],
      statusOptions: [
        {
          name: "取消",
          value: "-1",
          type: "info",
        },
        {
          name: "已支付",
          value: "1",
          type: "success",
        },
        {
          name: "未支付",
          value: "0",
          type: "danger",
        },
        {
          name: "全部",
          value: null,
          type: "info",
        },
      ],
      paymentOptions: [
        { value: "0", name: "年付" },
        { value: "1", name: "半年付" },
        { value: "2", name: "季付" },
        { value: "3", name: "月付" },
        { value: "4", name: "日付" },
      ],
      payDialogVisible: false,
      currentBill: null,
      payLoading: false,
      payForm: {
        operator: "",
      },
      payRules: {
        operator: [
          { required: true, message: "请输入操作人", trigger: "blur" },
        ],
      },
      roomAmount: {
        totalAmount: 0,
        remainingAmount: 0,
        paidAmount: 0,
      },
    };
  },
  computed: {
    roomId() {
      return this.$route.params.id || "";
    },
  },
  created() {
    this.queryParams.roomId = this.roomId;
    if (this.roomId) {
      this.fetchData();
      this.fetchRoomAmount();
      this.fetchRoomInfo();
    }
  },
  methods: {
    getPaymentTypeText(type) {
      // const typeStr = String(type);
      // const option = this.paymentOptions.find((item) => item.value === typeStr);
      // return option ? option.name : "未知方式";
      return (
        this.paymentOptions.find((e) => e.value == type) || {
          name: "未知",
          value: "",
          type: "",
        }
      );
    },
    statusFilter(status) {
      return (
        this.statusOptions.find((e) => e.value == status) || {
          name: "未知",
          value: "",
          type: "",
        }
      );
    },
    tableRowClassName({ row }) {
      return row.isExpired == 1 ? "expired-row" : "";
    },
    async fetchData() {
      try {
        this.loading = true;
        const res = await getRentBillsByPage(this.queryParams);
        this.billList = res.data.data || [];
        this.total = res.data.totalElements || 0;
      } catch (err) {
        console.error(err);
        //   this.$message.error("获取账单列表失败");
        this.billList = [];
      } finally {
        this.loading = false;
      }
    },
    // 每页数量变化
    handleSizeChange(val) {
      this.queryParams.pageSize = val;
      this.fetchData();
    },
    // 当前页变化
    handleCurrentChange(val) {
      this.queryParams.pageNo = val;
      this.fetchData();
    },
    handlePay(row) {
      this.currentBill = row;

      this.$confirm(
        Array.isArray(row) && row.length > 1
          ? `确认批量支付选中的${row.length}笔账单?`
          : "确认支付该账单?",
        "支付确认",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          this.submitPayConfirmed();
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消支付",
          });
        });
    },
    async submitPayConfirmed() {
      if (!this.currentBill) return;

      try {
        this.payLoading = true;
        const params = {
          billIds: Array.isArray(this.currentBill)
            ? this.currentBill
            : [this.currentBill],
          operator: this.$store.getters.name || "系统用户", // 使用当前登录用户名
        };

        await payRentBill(params);
        this.$message.success("账单支付成功");
        this.multipleSelection = [];
        this.$refs.multipleTable.clearSelection();

        // 刷新数据
        this.fetchData();
        this.fetchRoomAmount(); // 支付成功后刷新金额数据
      } catch (err) {
        console.error(err);
        this.$message.error("支付操作失败");
      } finally {
        this.payLoading = false;
        this.currentBill = null;
      }
    },
    async fetchRoomAmount() {
      try {
        this.amountLoading = true;
        const res = await getRoomAmount(this.roomId);
        if (res.code === 200) {
          this.roomAmount = res.data || {
            totalAmount: 0,
            remainingAmount: 0,
            paidAmount: 0,
          };
        } else {
          this.$message.error(res.message);
        }
      } catch (err) {
        console.error(err);
      } finally {
        this.amountLoading = false;
      }
    },
    handleSelectionChange(val) {
      console.log(val);
      this.multipleSelection = val.map((e) => e.id);
    },
    async fetchRoomInfo() {
      try {
        this.roomInfoLoading = true;
        const res = await getProjectRoomInfo(this.roomId);
        if (res.code === 200) {
          this.roomInfo = res.data || {};
        } else {
          this.$message.error(res.message || "获取房屋信息失败");
        }
      } catch (err) {
        console.error(err);
        this.$message.error("获取房屋信息失败");
      } finally {
        this.roomInfoLoading = false;
      }
    },
  },
};
</script>
    
<style scoped lang="scss">
.pagination {
  padding-top: 20px;
  text-align: right;
}
.text-muted {
  color: #909399;
}
.box-card {
  margin-bottom: 20px;
}
.amount-text {
  font-weight: bold;
  font-size: 16px;
}
.success-text {
  color: #67c23a;
}
.danger-text {
  color: #f56c6c;
}

.expired-row {
  background-color: #f0f9eb; /* 淡绿色背景 */
}

.report-data {
  margin-bottom: 40px;

  .rent-info-container {
    width: 100%;
    margin-bottom: 20px;
  }

  .rent-info-card {
    // border-radius: 8px;

    .card-header {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: bold;

      i {
        margin-right: 8px;
        color: #409eff;
      }
    }

    .room-info-section {
      margin-bottom: 20px;
    }

    .amount-row {
      display: flex;
      justify-content: space-around;
      flex-wrap: wrap;
      // padding: 10px 0;
      margin-top: 20px;
      border-top: 1px dashed #ebeef5;
      padding-top: 15px;
    }

    .amount-item {
      text-align: center;
      padding: 0px 20px;
      min-width: 150px;

      .amount-label {
        font-size: 14px;
        color: #909399;
        margin-bottom: 10px;
      }

      .amount-value {
        font-size: 28px;
        font-weight: bold;

        .unit {
          font-size: 14px;
          font-weight: normal;
          margin-left: 4px;
        }

        &.total {
          color: #303133;
        }

        &.paid {
          color: #67c23a;
        }

        &.remaining {
          color: #f56c6c;
        }
      }
    }
  }

  .value-display {
    font-size: 22px;
    color: #303133;
    padding: 0 15px;
    display: block;
    text-align: center;
  }

  .data-item {
    text-align: center;
    .label {
      font-size: 14px;
      color: #909399;
      margin-bottom: 8px;
    }
    .value {
      font-size: 20px;
      color: #303133;
      font-weight: bold;
    }
  }
}

.room-info-card {
  margin-bottom: 20px;

  .card-header {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: bold;

    i {
      margin-right: 8px;
      color: #409eff;
    }
  }
}
</style>

<style>
/* 全局样式，确保可以覆盖组件内部样式 */
.el-table .expired-row {
  background-color: #35f7350a !important;
}

.filter-container {
  padding-bottom: 20px;
}
.pagination {
  padding-top: 20px;
  text-align: right;
}

::v-deep .el-descriptions__header {
  margin-bottom: 10px;
}

.room-info-section ::v-deep .el-descriptions__header {
  font-size: 14px;
  font-weight: normal;
  color: #606266;
}

.room-info-section ::v-deep .el-descriptions__title {
  font-size: 14px;
  color: #606266;
}

.room-info-section ::v-deep .el-descriptions-item__label {
  font-weight: normal;
}
</style>