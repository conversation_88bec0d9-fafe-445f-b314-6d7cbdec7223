# 系统配置管理接口文档
功能模块:
一级菜单:系统配置 二级目录自己看着来
1、商品分成比例/结算天数
2、免提现额度设置
3、 分成比例设置(物业)
5、首登积分设置
7、设置取消支付回退积分时长 分钟
6、默认头像设置


## 基础信息
- 基础路径：/sw/admin/system/config
- 认证方式：Bearer Token
- 返回格式：JSON

## 数据结构

### SystemConfig 对象
字段名 | 类型 | 说明
------|------|------
id | Long | 主键ID，对应配置键名
template | String | 配置项的值

### ResultDto 通用返回
{
  "code": 200,
  "message": "success",
  "data": {}
}

## 接口列表

### 1. 获取免审核提现额度配置
- URL: /getWithdrawal
- Method: GET
- 描述: 获取免审核的最低提现金额配置
- 响应示例:
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "NO_REVIEW_WITHDRAWAL_MONEY_KEY",
    "template": "1"
  }
}

### 2. 获取商品返积分比例
- URL: /getGoodsUserPercent
- Method: GET
- 描述: 获取商品交易的积分返还比例
- 响应示例:
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "GOODS_USER_PERCENT_KEY",
    "template": "50"
  }
}

### 3. 获取订单返积分天数
- URL: /getGoodsEndTime
- Method: GET
- 描述: 获取订单完成后返还积数的等待天数
- 响应示例:
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "GOODS_END_TIME_KEY",
    "template": "21"
  }
}

### 4. 获取物业分成比例
- URL: /getWyPlatformDividend
- Method: GET
- 描述: 获取物业与平台的分成比例（物业端）
- 响应示例:
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "WY_PLATFORM_DIVIDEND_KEY",
    "template": "50"
  }
}

### 5. 获取默认头像配置
- URL: /getDefaultHeadUrl
- Method: GET
- 描述: 获取系统默认头像URL
- 响应示例:
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "DEFAULT_HEAD_URL_KEY",
    "template": "https://cdn.anxiaoye.com/axy/imgs/1897953574139334656.png"
  }
}

### 6. 获取首次登录积分
- URL: /getFirstBindPhonePoints
- Method: GET
- 描述: 获取用户首次绑定手机号奖励积分数
- 响应示例:
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "FIRST_BIND_PHONE_POINTS_KEY",
    "template": "30"
  }
}

### 7. 获取支付超时时间
- URL: /getOrderPayTimeout
- Method: GET
- 描述: 获取订单支付超时时间（分钟）
- 响应示例:
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "ORDER_PAY_TIMEOUT_KEY",
    "template": "5"
  }
}

### 8. 更新系统配置
- URL: /setSystemConfig
- Method: POST 
- content-type:application/json
- 请求参数:
{
  "id": "配置键名",
  "template": "新值"
}
- 请求示例:
{
  "id": "1",
  "template": ""
}
- 成功响应:
{
  "code": 200,
  "message": "success",
  "data": null
}
