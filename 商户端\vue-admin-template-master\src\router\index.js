import Vue from "vue";
import Router from "vue-router";

Vue.use(Router);

/* Layout */
import Layout from "@/layout";
import Login from "@/views/login/index";


/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'/'el-icon-x' the icon show in the sidebar
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    path: "/login",
    component: Login,
    hidden: true,
  },

  {
    path: "/404",
    component: () => import("@/views/404"),
    hidden: true,
  },

  {
    path: "/refresh",
    component: () => import("@/views/refresh"),
    hidden: true,
  },

  {
    path: "/",
    component: Layout,
    redirect: "/userInfo",
    meta: { title: "资料管理" },
    alwaysShow: true,
    children: [
      {
        path: "userInfo",
        name: "UserInfo",
        component: () => import("@/views/userInfo/index"),
        meta: { title: "完善资料" },
      },
    ],
  },
  {
    path: "/profile",
    component: Layout,
    redirect: "/profile/storeoverview",
    meta: { title: "数据展示" },
    alwaysShow: true,
    children: [
      {
        path: "storeoverview",
        name: "Storeoverview",
        component: () => import("@/views/storeoverview/index"),
        meta: { title: "店铺概况" },
      },
      {
        path: "statistics",
        name: "Statistics",
        component: () => import("@/views/statistics/index"),
        meta: { title: "数据展示" },
      },
      // {
      //   path: "review",
      //   name: "Review",
      //   component: () => import("@/views/review/index"),
      //   meta: { title: "评论管理" },
      // },
    ],
  },

  // 404 page must be placed at the end !!!
  { path: "*", redirect: "/404", hidden: true },
];

const createRouter = () =>
  new Router({
    // mode: 'history', // require service support
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRoutes,
  });

const router = createRouter();

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter();
  router.matcher = newRouter.matcher; // reset router
}

export default router;
