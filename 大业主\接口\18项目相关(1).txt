1、添加项目
/sw/wy/openProject/addProject
post
{
 
  "projectName": "项目名称",
  "address": "地址",
  "linkPerson": "联系人",
  "linkPhone": "联系电话",
  "openKey": "大业主所属"
}

响应:
{
	code:200,
	data:null
}



2、根据id获取项目信息
/sw/wy/openProject/getProjectInfo
get
id:
响应:
{
	code:200,
	data:{
	id:
	  "projectName": "项目名称",
  "address": "地址",
  "linkPerson": "联系人",
  "linkPhone": "联系电话",
  "openKey": "大业主所属"
  createTime:创建时间
	}
}

3、修改项目
/sw/wy/openProject/modifyProject
post
{
 	id:
  "linkPerson": "联系人",
  "linkPhone": "联系电话",
}

响应:
{
	code:200,
	data:null
}

4、获取项目列表
/sw/wy/openProject/getPage

post
{
	pageNo:
	pageSize:
}

响应:
{
	code: 业务响应码 200 500...  
	message: 这个是异常的时候的消息提示
	data:{
		pageNo: '第几页'
		pageSize:'一页多少页'
		totalPages:'总页数'
		totalElements:'总数据量'
		data:[

		{
	id:
	  "projectName": "项目名称",
  "address": "地址",
  "linkPerson": "联系人",
  "linkPhone": "联系电话",
  "openKey": "大业主所属"
  createTime:创建时间
	}
			]
	}
}