/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-05-27 15:01:09
 * @LastEditors: wangxingyue <EMAIL>
 * @LastEditTime: 2025-06-27 19:03:34
 * @FilePath: \ban-ban\运营端\admin\src\api\merchant.js
 */
import request from "@/utils/request";

// 商户运营数据
export function getMerchantReport(data) {
  return request({
    url: "/admin/report/getMerchantReport",
    method: "post",
    requestType: "json",
    data,
  });
}

// 商户列表
// export function getMerchantList(data) {
//   return request({
//     url: "/admin/sh/getMerchantList",
//     method: "post",
//     requestType: "json",
//     data,
//   });
// }
// ---> 20250625 商户列表
export function getShopList(data) {
  return request({
    url: "/admin/shop/getShopList",
    method: "post",
    requestType: "json",
    data,
  });
}

// 批量通过
export function passShops(params) {
  return request({
    url: "/admin/shop/passShops",
    method: "get",
    // requestType: "form",
    params,
  });
}

// 审核不通过
export function rejectShop(data) {
  return request({
    url: "/admin/shop/rejectShop",
    method: "post",
    requestType: "json",
    data
  });
}


// 获取基础类型与类型列表
export function getAllBaseTypeList() {
  return request({
    url: "/admin/service/type/getAllBaseTypeList",
    method: "get"
  });
}

// 添加基础类型
export function addBaseType(data) {
  return request({
    url: "/admin/service/type/addBaseType",
    method: "post",
    requestType: "json",
    data
  });
}

// 修改基础类型
export function modifyBaseType(data) {
  return request({
    url: "/admin/service/type/modifyBaseType",
    method: "post",
    requestType: "json",
    data
  });
}

// 添加商户类型
export function addServiceType(data) {
  return request({
    url: "/admin/service/type/addServiceType",
    method: "post",
    requestType: "json",
    data
  });
}

// 修改商户类型
export function modifyServiceType(data) {
  return request({
    url: "/admin/service/type/modifyServiceType",
    method: "post",
    requestType: "json",
    data
  });
}

// 删除服务类型
export function removeServiceType(id) {
  return request({
    url: "/admin/service/type/removeServiceType",
    method: "get",
    params: { id }
  });
}

// 获取待审核商户数量
export function getWaitShopNum() {
  return request({
    url: "/admin/shop/getWaitShopNum",
    method: "get"
  });
}