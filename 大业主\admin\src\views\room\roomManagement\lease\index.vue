<template>
  <div class="">
    <!-- 房屋信息展示 -->
    <div style="margin-bottom: 10px">
      <el-descriptions
        title="房屋信息"
        :column="3"
        border
        v-loading="roomInfoLoading"
      >
        <el-descriptions-item label="项目名称">{{
          roomInfo.projectName
        }}</el-descriptions-item>
        <el-descriptions-item label="楼号">{{
          roomInfo.buildingNumber
        }}</el-descriptions-item>
        <el-descriptions-item label="房间号">{{
          roomInfo.roomNumber
        }}</el-descriptions-item>
        <!-- <el-descriptions-item label="面积(平方米)">{{
          roomInfo.area
        }}</el-descriptions-item>
        <el-descriptions-item label="电表号">{{
          roomInfo.electricityMeterNumber
        }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="roomInfo.status == '0' ? 'success' : 'info'">
            {{ roomInfo.status == '0' ? '已租' : '待租' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="报价(元/月)">{{
          roomInfo.price
        }}</el-descriptions-item>
        <el-descriptions-item label="房间图片">
          <el-image 
            v-if="roomInfo.imageUrl" 
            :src="roomInfo.imageUrl" 
            style="width: 100px; height: 100px;"
            :preview-src-list="[roomInfo.imageUrl]">
          </el-image>
          <span v-else>暂无图片</span>
        </el-descriptions-item>
        <el-descriptions-item label="房间视频">
          <video 
            v-if="roomInfo.videoUrl" 
            :src="roomInfo.videoUrl" 
            controls 
            style="width: 200px; max-height: 150px;">
            您的浏览器不支持视频标签
          </video>
          <span v-else>暂无视频</span>
        </el-descriptions-item> -->
      </el-descriptions>
    </div>

    <!-- 添加操作按钮区域 -->
    <div class="filter-container">
      <el-button
        type="primary"
        icon="el-icon-plus"
        @click="handleAdd"
        v-if="!currentLease || String(currentLease.leaseStatus) !== '0'"
        >添加</el-button
      >
    </div>

    <el-table
      v-loading="loading"
      :data="currentLeaseData"
      max-height="580"
      style="width: 100%"
      element-loading-text="加载中"
      border
      fit
      highlight-current-row
    >
      <el-table-column label="承租人姓名" align="center" prop="tenantName" />
      <el-table-column label="联系电话" align="center" prop="phone" />
      <el-table-column label="所属行业" align="center" prop="industry" />
      <el-table-column label="起租日期" align="center" prop="startDate">
        <template #default="{ row }">
          {{ parseTime(row.startDate, "YYYY-MM-DD") }}
        </template>
      </el-table-column>
      <el-table-column label="到期日期" align="center" prop="endDate">
        <template #default="{ row }">
          {{ parseTime(row.endDate, "YYYY-MM-DD") }}
        </template>
      </el-table-column>
      <el-table-column label="月租金(元)" align="center" prop="monthlyRent" />
      <el-table-column label="押金(元)" align="center" prop="deposit" />
      <el-table-column label="付款方式" align="center">
        <template #default="{ row }">
          {{ getPaymentTypeText(row.paymentType) }}
        </template>
      </el-table-column>
      <el-table-column label="租赁状态" align="center">
        <template #default="{ row }">
          <el-tag :type="statusFilter(row.leaseStatus).type">
            {{ statusFilter(row.leaseStatus).name }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" v-if="false" />
      <el-table-column label="客户类型" align="center" v-if="false">
        <template #default="{ row }">
          {{ row.status === "0" ? "当前客户" : "结束租赁客户" }}
        </template>
      </el-table-column>
      <el-table-column
        label="总租金"
        align="center"
        prop="totalAmount"
        v-if="false"
      />
      <el-table-column
        label="已付租金"
        align="center"
        prop="payedAmount"
        v-if="false"
      />
      <el-table-column label="操作" align="center" width="120">
        <template #default="{ row }">
          <el-button type="text" size="mini" @click="handleEdit(row)"
            >修改</el-button
          >
          <el-button type="text" size="mini" @click="termination(row)"
            >终止</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 表单抽屉 -->
    <el-drawer
      :title="isAdd ? '添加租赁信息' : '修改租赁信息'"
      :visible.sync="drawerVisible"
      direction="rtl"
      size="40%"
      :before-close="handleClose"
    >
      <div class="drawer-container">
        <!-- 房屋管理员表格 -->
        <div class="manager-table-container" v-if="!isAdd">
          <div class="manager-header">
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-plus"
              @click="handleAddManager"
              >添加管理员</el-button
            >
          </div>
          <el-table
            v-loading="managerLoading"
            :data="managerList"
            border
            size="mini"
            style="width: 70%; margin-bottom: 50px"
          >
            <el-table-column
              label="序号"
              type="index"
              width="50"
              align="center"
            ></el-table-column>
            <el-table-column
              label="姓名"
              prop="name"
              align="center"
            ></el-table-column>
            <el-table-column
              label="联系电话"
              prop="telephone"
              align="center"
            ></el-table-column>
            <el-table-column label="操作" width="120" align="center">
              <template #default="{ row }">
                <el-button
                  type="text"
                  size="mini"
                  @click="handleDeleteManager(row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>

        <el-dialog
          title="添加房屋管理员"
          :visible.sync="managerDialogVisible"
          width="30%"
          :append-to-body="true"
        >
          <el-form
            :model="managerForm"
            ref="managerForm"
            label-width="100px"
            :rules="managerRules"
          >
            <el-form-item label="管理员姓名" prop="name">
              <el-input
                v-model="managerForm.name"
                placeholder="请输入管理员姓名"
              ></el-input>
            </el-form-item>
            <el-form-item label="联系电话" prop="telephone">
              <el-input
                v-model="managerForm.telephone"
                placeholder="请输入联系电话"
              ></el-input>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="managerDialogVisible = false">取 消</el-button>
            <el-button
              type="primary"
              @click="submitAddManager"
              :loading="addManagerLoading"
              >确 定</el-button
            >
          </div>
        </el-dialog>

        <el-form
          :model="editForm"
          :rules="rules"
          ref="editForm"
          label-width="100px"
        >
          <el-form-item label="承租人姓名" prop="tenantName">
            <el-input v-model="editForm.tenantName"></el-input>
          </el-form-item>
          <el-form-item label="联系电话" prop="phone">
            <el-input v-model="editForm.phone"></el-input>
          </el-form-item>
          <el-form-item label="所属行业" prop="industry">
            <el-input v-model="editForm.industry"></el-input>
          </el-form-item>
          <el-form-item label="起租日期" prop="startDate">
            <el-date-picker
              v-model="editForm.startDate"
              type="date"
              placeholder="选择日期"
              :disabled="!isAdd"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="到期日期" prop="endDate">
            <el-date-picker
              v-model="editForm.endDate"
              type="date"
              placeholder="选择日期"
              :disabled="!isAdd"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="月租金(元)" prop="monthlyRent">
            <el-input-number
              v-model="editForm.monthlyRent"
              :min="0"
            ></el-input-number>
          </el-form-item>
          <el-form-item label="押金(元)" prop="deposit">
            <el-input-number
              v-model="editForm.deposit"
              :min="0"
            ></el-input-number>
          </el-form-item>
          <el-form-item label="付款方式" prop="paymentType">
            <el-select
              v-model="editForm.paymentType"
              placeholder="请选择付款方式"
            >
              <el-option
                v-for="(item, index) in paymentOptions"
                :key="index + item.value"
                :label="item.name"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="租赁状态" prop="leaseStatus">
            <el-select v-model="editForm.leaseStatus">
              <el-option label="已租" value="0"></el-option>
              <el-option label="合同期内欠费" value="1"></el-option>
              <el-option label="合同已经到期" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="备注信息" prop="remark">
            <el-input type="textarea" v-model="editForm.remark"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSave" :loading="saveLoading"
              >确 定</el-button
            >
            <el-button @click="drawerVisible = false">取 消</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-drawer>

    <!-- 终止租赁关系对话框 -->
    <el-dialog
      title="终止租赁关系"
      :visible.sync="terminationVisible"
      width="30%"
      :close-on-click-modal="false"
    >
      <el-form
        :model="terminationForm"
        ref="terminationForm"
        label-width="100px"
      >
        <el-form-item label="终止类型" prop="shopType">
          <el-select
            v-model="terminationForm.shopType"
            placeholder="请选择终止类型"
          >
            <el-option label="合同到期终止" :value="0"></el-option>
            <el-option label="合同未到期终止" :value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="备注信息"
          prop="remark"
          v-if="terminationForm.shopType === 1"
        >
          <el-input
            type="textarea"
            v-model="terminationForm.remark"
            placeholder="请输入终止原因或备注信息"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="terminationVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="handleTerminationSubmit"
          :loading="terminationLoading"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
  
<script>
import {
  getLeaseInfo,
  modifyLeaseInfo,
  addLeaseInfo,
  stopLease,
  getProjectRoomInfo,
  getAllManager,
  addRoomManager,
  removeRoomManager,
} from "@/api/room";

export default {
  data() {
    return {
      paymentOptions: [
        {
          name: "年付",
          value: "0",
        },
        {
          name: "半年付",
          value: "1",
        },
        {
          name: "季付",
          value: "2",
        },
        {
          name: "月付",
          value: "3",
        },
        {
          name: "日付",
          value: "4",
        },
      ],
      currentLease: null,
      loading: true,
      drawerVisible: false,
      saveLoading: false,
      isAdd: false, // 区分添加和编辑模式
      editForm: {
        id: "",
        tenantName: "",
        phone: "",
        industry: "",
        startDate: "",
        endDate: "",
        monthlyRent: 0,
        deposit: 0,
        paymentType: "",
        leaseStatus: "",
        remark: "",
        roomId: "",
      },
      rules: {
        tenantName: [
          { required: true, message: "请输入承租人姓名", trigger: "blur" },
        ],
        phone: [{ required: true, message: "请输入联系电话", trigger: "blur" }],
        industry: [
          { required: true, message: "请输入所属行业", trigger: "blur" },
        ],
        startDate: [
          { required: true, message: "请选择起租日期", trigger: "change" },
        ],
        endDate: [
          { required: true, message: "请选择到期日期", trigger: "change" },
        ],
        monthlyRent: [
          { required: true, message: "请输入月租金", trigger: "blur" },
        ],
        deposit: [{ required: true, message: "请输入押金", trigger: "blur" }],
        paymentType: [
          { required: true, message: "请选择付款方式", trigger: "change" },
        ],
        leaseStatus: [
          { required: true, message: "请选择租赁状态", trigger: "change" },
        ],
      },
      terminationVisible: false,
      terminationLoading: false,
      terminationForm: {
        id: "",
        shopType: "",
        remark: "",
      },
      roomInfo: {
        id: "",
        openKey: "",
        projectName: "",
        buildingNumber: "",
        roomNumber: "",
        area: "",
        electricityMeterNumber: "",
        status: "",
        price: "",
        imageUrl: "",
        videoUrl: "",
      },
      roomInfoLoading: true,

      // 管理员相关数据
      managerList: [],
      managerLoading: false,
      managerDialogVisible: false,
      managerForm: {
        name: "",
        telephone: "",
        roomId: "",
        leaseInfoId: "",
      },
      managerRules: {
        name: [
          { required: true, message: "请输入管理员姓名", trigger: "blur" },
        ],
        telephone: [
          { required: true, message: "请输入联系电话", trigger: "blur" },
        ],
      },
      addManagerLoading: false,
    };
  },
  computed: {
    roomId() {
      return this.$route.params.id || "";
    },
    currentLeaseData() {
      return this.currentLease ? [this.currentLease] : [];
    },
  },
  created() {
    this.fetchData();
    this.fetchRoomInfo();
  },
  methods: {
    statusFilter(status) {
      const statusMap = {
        0: { name: "已租", type: "success" },
        1: { name: "欠费", type: "danger" },
        2: { name: "已到期", type: "warning" },
      };
      return statusMap[status] || { name: "未知状态", type: "info" };
    },
    getPaymentTypeText(type) {
      const typeStr = String(type);
      const option = this.paymentOptions.find((item) => item.value === typeStr);
      return option ? option.name : "未知方式";
    },
    async fetchData() {
      try {
        this.loading = true;
        const res = await getLeaseInfo(this.roomId);
        if (res.code === 200) {
          this.currentLease = res.data || null;
        } else {
          this.$message.error(res.message || "获取当前租赁信息失败");
          this.currentLease = null;
        }
      } catch (err) {
        console.error(err);
        this.$message.error("获取当前租赁信息失败");
        this.currentLease = null;
      } finally {
        this.loading = false;
      }
    },
    handleEdit(row) {
      this.isAdd = false;
      this.editForm = JSON.parse(JSON.stringify(row));
      // 确保付款方式和租赁状态的值是字符串类型
      this.editForm.paymentType = String(this.editForm.paymentType);
      this.editForm.leaseStatus = String(this.editForm.leaseStatus);
      this.drawerVisible = true;

      // 获取管理员列表
      this.fetchManagerList();
    },
    termination(row) {
      this.terminationForm = {
        id: row.id,
        shopType: "",
        remark: "",
      };
      this.terminationVisible = true;
    },
    handleAdd() {
      this.isAdd = true;
      this.editForm = {
        tenantName: "",
        phone: "",
        industry: "",
        startDate: "",
        endDate: "",
        monthlyRent: 0,
        deposit: 0,
        paymentType: "0",
        leaseStatus: "0",
        remark: "",
        roomId: this.roomId,
      };
      this.drawerVisible = true;
    },
    handleClose(done) {
      done();
    },
    handleSave() {
      this.$refs.editForm.validate(async (valid) => {
        if (!valid) return;

        try {
          this.saveLoading = true;

          if (this.isAdd) {
            // 添加操作
            const params = {
              ...this.editForm,
              roomId: this.roomId,
            };

            const res = await addLeaseInfo(params);

            if (res.code === 200) {
              this.$message.success("添加租赁信息成功");
              this.drawerVisible = false;
              this.fetchData(); // 刷新数据
            } else {
              this.$message.error(res.message);
            }
          } else {
            // 修改操作
            const params = {
              ...this.editForm,
              roomId: this.roomId,
            };

            const res = await modifyLeaseInfo(params);
            if (res.code === 200) {
              this.$message.success("修改租赁信息成功");
              this.drawerVisible = false;
              this.fetchData(); // 刷新数据
            } else {
              this.$message.error(res.message);
            }
          }
        } catch (err) {
          console.error(err);
          // this.$message.error(this.isAdd ? '添加租赁信息失败' : '修改租赁信息失败');
        } finally {
          this.saveLoading = false;
        }
      });
    },
    async handleTerminationSubmit() {
      if (
        !this.terminationForm.shopType &&
        this.terminationForm.shopType !== 0
      ) {
        this.$message.warning("请选择终止类型");
        return;
      }

      if (this.terminationForm.shopType === 1 && !this.terminationForm.remark) {
        this.$message.warning("请输入终止原因");
        return;
      }

      try {
        this.terminationLoading = true;

        const res = await stopLease(this.terminationForm);

        if (res.code === 200) {
          this.$message.success("终止租赁关系成功");
          this.terminationVisible = false;
          this.fetchData(); // 刷新数据
        } else {
          this.$message.error(res.message || "终止租赁关系失败");
        }
      } catch (err) {
        console.error(err);
        this.$message.error("终止租赁关系失败");
      } finally {
        this.terminationLoading = false;
      }
    },
    async fetchRoomInfo() {
      try {
        this.roomInfoLoading = true;
        const res = await getProjectRoomInfo(this.roomId);
        if (res.code === 200) {
          this.roomInfo = res.data || {};
        } else {
          this.$message.error(res.message || "获取房屋信息失败");
          this.roomInfo = {};
        }
      } catch (err) {
        console.error(err);
        this.$message.error("获取房屋信息失败");
        this.roomInfo = {};
      } finally {
        this.roomInfoLoading = false;
      }
    },

    // 管理员相关方法
    async fetchManagerList() {
      if (!this.editForm.id) return;

      try {
        this.managerLoading = true;
        const res = await getAllManager(this.roomId);
        if (res.code === 200) {
          this.managerList = res.data || [];
        } else {
          this.$message.error(res.message || "获取房屋管理员列表失败");
        }
      } catch (err) {
        console.error(err);
        this.$message.error("获取房屋管理员列表失败");
      } finally {
        this.managerLoading = false;
      }
    },

    handleAddManager() {
      this.managerForm = {
        name: "",
        telephone: "",
        roomId: this.roomId,
        leaseInfoId: this.editForm.id,
      };
      this.managerDialogVisible = true;
    },

    async submitAddManager() {
      this.$refs.managerForm.validate(async (valid) => {
        if (!valid) return;

        try {
          this.addManagerLoading = true;
          const res = await addRoomManager(this.managerForm);
          if (res.code === 200) {
            this.$message.success("添加管理员成功");
            this.managerDialogVisible = false;
            this.fetchManagerList(); // 刷新管理员列表
          } else {
            this.$message.error(res.message || "添加管理员失败");
          }
        } catch (err) {
          console.error(err);
          this.$message.error("添加管理员失败");
        } finally {
          this.addManagerLoading = false;
        }
      });
    },

    async handleDeleteManager(row) {
      try {
        await this.$confirm("确认删除此管理员？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });

        const res = await removeRoomManager(row.id);
        if (res.code === 200) {
          this.$message.success("删除管理员成功");
          this.fetchManagerList(); // 刷新管理员列表
        } else {
          this.$message.error(res.message || "删除管理员失败");
        }
      } catch (err) {
        if (err === "cancel") return;
        console.error(err);
        this.$message.error("删除管理员失败");
      }
    },
  },
};
</script>
  
<style scoped lang="scss">
.pagination {
  padding-top: 20px;
  text-align: right;
}
.filter-container {
  padding-bottom: 10px;
}

::v-deep .el-descriptions__header {
  margin-bottom: 10px !important;
}

.manager-table-container {
  padding: 0 20px;
  margin-bottom: 20px;
}

.manager-header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 10px;

  h3 {
    margin: 0;
  }
}
</style>