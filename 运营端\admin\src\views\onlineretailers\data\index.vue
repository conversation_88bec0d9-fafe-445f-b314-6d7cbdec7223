<!--
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-05-27 10:49:48
 * @LastEditors: wangxingyue
 * @LastEditTime: 2025-07-24 16:03:57
 * @FilePath: \ban-ban\运营端\admin\src\views\onlineretailers\data\index.vue
-->

<template>
  <div class="app-container">
    <el-table
      v-loading="listLoading"
      :data="list"
      max-height="620"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
	  <el-table-column label="日期" align="center" prop="reportDate">
		<template #default="{ row }">{{ parseTime(row.reportDate).slice(0,10)}}</template>
	  </el-table-column>
      <el-table-column label="日活" align="center" prop="dailyActiveUsers" />
      <el-table-column label="订单数" align="center" prop="orderCount" />
      <el-table-column label="订单额" align="center" prop="orderAmount"></el-table-column>
      <el-table-column label="预计返佣" align="center" prop="estimateAmount" />
      <!-- 如果actualUserAmount大于0，使用actualUserAmount，否则 使用userAmount -->
      <el-table-column label="给出" align="center">
        <template #default="{ row }">{{ row.actualUserAmount > 0 ? row.actualUserAmount : row.userAmount }}</template>
      </el-table-column>
      <el-table-column label="实际返佣" align="center" prop="actualAmount" />
    </el-table>

    <div class="pagination">
      <el-pagination
        @size-change="fetchData"
        @current-change="fetchData"
        :current-page.sync="querySearch.pageNo"
        :page-sizes="[15, 30, 50, 100]"
        :page-size.sync="querySearch.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="querySearch.total"
      >
      </el-pagination>
    </div>


  </div>
</template>

<script>
import * as api from "@/api/onlineretailers";
export default {
  name: "Onlineretailersdata",
  data() {
    return {
      list: null,
      listLoading: true,
      querySearch: {
        pageNo: 1,
        pageSize: 50,
        total: 0,
      }
    };
  },
  created() {
    this.fetchData();
  },
  methods: {
    async fetchData() {
      try {
        this.listLoading = true;
        const { data: res } = await api.getEcommerceReport(
          this.querySearch
        );
        const { data, pageNo, pageSize, totalElements } = res;
        this.list = data;
        this.querySearch = {
          pageNo,
          pageSize,
          total: totalElements,
          status: this.querySearch.status,
        };
      } catch (err) {
        console.error(err);
      } finally {
        this.listLoading = false;
      }
    },

 
 
  },
};
</script>

<style scoped lang="scss">
.pagination {
  padding-top: 20px;
  text-align: right;
}
</style>
