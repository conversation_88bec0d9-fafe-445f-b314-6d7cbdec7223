/*
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-05-27 15:02:06
 * @LastEditors: wangxingyue
 * @LastEditTime: 2025-06-25 10:41:28
 * @FilePath: \ban-ban\运营端\admin\src\api\withdrawal.js
 */
import request from "@/utils/request";

// 提现记录
export function getAllWithdrawalList(data) {
  return request({
    url: "/admin/wy/info/getAllWithdrawalList",
    method: "post",
    requestType: "json",
    data,
  });
}

// 已经提现
// /sw/admin/wy/info/passWithdrawal

// post:
// ids: 1,2,3

// 响应:{
// 	code:200,
// 	message:null,
// 	data:null
// }
export function passWithdrawal(data) {
  return request({
    url: "/admin/wy/info/passWithdrawal",
    method: "post",
    requestType: "form",
    data,
  });
}

// 拒绝提现
// /sw/admin/wy/info/rejectWithdrawal
// post:
// ids: 1,2,3

// 响应:{
// 	code:200,
// 	message:null,
// 	data:null
// }
export function rejectWithdrawal(data) {
  return request({
    url: "/admin/wy/info/rejectWithdrawal",
    method: "post",
    requestType: "form",
    data,
  });
}


// 小程序提现管理
// 获取提现申请列表
export function getWithdrawalOrders(data) {
  return request({
    url: "/admin/withdrawal/getWithdrawalOrders",
    method: "post",
    requestType: "json",
    data,
  });
}

// 批量通过
// /sw/admin/withdrawal/passOrders

// GET
// ids:1,2,3
// 响应:
// {
// 	code:200,
// 	data:null
// }
export function passOrders(params) {
  return request({
    url: "/admin/withdrawal/passOrders",
    method: "get",
    // requestType: "form",
    params,
  });
}

//   批量拒绝
// /sw/admin/withdrawal/rejectOrders
// GET
// ids:1,2,3
// 响应:
// {
// 	code:200,
// 	data:null
// }
export function rejectOrders(params) {
  return request({
    url: "/admin/withdrawal/rejectOrders",
    method: "get",
    // requestType: "form",
    params,
  });
}