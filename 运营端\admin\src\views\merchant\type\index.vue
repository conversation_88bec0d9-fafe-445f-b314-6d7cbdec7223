<template>
  <div class="wrap" v-loading="listLoading">
    <div class="btnBox">
      <el-button type="primary" @click="openAddType()" size="small"
        >新增类型</el-button
      >
    </div>
    <el-table
      :data="tableData"
      row-key="baseId"
      border
      :expand-row-keys="expandRowKeys"
      highlight-current-row
    >
      <el-table-column type="expand">
        <template #default="{ row: typeRow }">
          <el-table
            :data="typeRow.serviceTypeItemVoList"
            size="small"
            border
            highlight-current-row
            style="width: 90%; margin: 0 auto"
          >
            <el-table-column
              label="商户类型名称"
              prop="serviceTypeName"
              align="center"
            />
            <el-table-column
              label="服务描述"
              prop="description"
              align="center"
            />
            <el-table-column label="主页图片" prop="description" align="center">
              <template #default="{ row: phoneRow }">
                <el-image
                  style="width: 50px; height: 50px"
                  :src="phoneRow.iconUrl"
                  fit="contain"
                  :preview-src-list="[phoneRow.iconUrl]"
                />
              </template>
            </el-table-column>
             <el-table-column label="类别图片" prop="description" align="center">
              <template #default="{ row: phoneRow }">
                <el-image
                  style="width: 50px; height: 50px"
                  :src="phoneRow.imageUrl"
                  fit="contain"
                  :preview-src-list="[phoneRow.imageUrl]"
                />
              </template>
            </el-table-column>
            <el-table-column label="主页排序" prop="homeSort" align="center" />
            <el-table-column label="页面排序" prop="pageSort" align="center" />
            <el-table-column label="操作" width="200" align="center">
              <template #default="{ row: phoneRow }">
                <el-button type="text" @click="openAddPhone(typeRow, phoneRow)"
                  >修改</el-button
                >
                <el-button type="text" @click="delServiceType(phoneRow)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </template>
      </el-table-column>
      <el-table-column label="基础类型名称" prop="baseName" align="center">
      </el-table-column>
      <el-table-column label="操作" width="300" align="center">
        <template #default="{ row }">
          <el-button type="text" @click="openAddPhone(row)">新增</el-button>
          <el-button type="text" @click="openAddType(row)">修改</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      :title="`${typeDialog.isEdit ? '修改' : '新增'}基础类型`"
      :visible.sync="typeDialog.isShow"
      width="32%"
      :before-close="handleClose"
      center
      :disabled="typeDialog.loading"
    >
      <el-form
        :model="typeDialog.form"
        label-width="140px"
        :rules="rules"
        ref="typeForm"
      >
        <el-form-item label="类型名称：" prop="baseTypeName">
          <el-input
            v-model="typeDialog.form.baseTypeName"
            placeholder="请输入类型名称"
          ></el-input>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose" :disabled="typeDialog.loading"
          >取 消</el-button
        >
        <el-button
          type="primary"
          @click="submitTypeDialog()"
          :loading="typeDialog.loading"
          >确 定</el-button
        >
      </span>
    </el-dialog>

    <el-dialog
      :title="`${phoneDialog.isEdit ? '修改' : '新增'}商户类型`"
      :visible.sync="phoneDialog.isShow"
      width="32%"
      :before-close="handleClose"
      center
      :disabled="phoneDialog.loading"
    >
      <el-form
        :model="phoneDialog.form"
        label-width="140px"
        :rules="rules"
        ref="phoneForm"
        :disabled="phoneDialog.loading"
      >
        <el-form-item label="商户类型名称：" prop="serviceTypeName">
          <el-input
            v-model="phoneDialog.form.serviceTypeName"
            placeholder="请输入商户类型名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="服务描述：" prop="description">
          <el-input
            v-model="phoneDialog.form.description"
            placeholder="请输入服务描述"
          ></el-input>
        </el-form-item>
        <el-form-item label="主页图片：" prop="iconUrl">
          <el-upload
            class="avatar-uploader"
            :action="`${BASE_URL}/axy/file/uploadFile`"
            :show-file-list="false"
            :on-success="handleSuccess1"
            :before-upload="handleUpload"
            :on-remove="() => (phoneDialog.form.iconUrl = '')"
          >
            <img
              v-if="phoneDialog.form.iconUrl"
              :src="phoneDialog.form.iconUrl"
              style="width: 100px; height: 100px"
            />
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>
        <el-form-item label="类别图片：" prop="imageUrl">
          <el-upload
            class="avatar-uploader"
            :action="`${BASE_URL}/axy/file/uploadFile`"
            :show-file-list="false"
            :on-success="handleSuccess"
            :before-upload="handleUpload"
            :on-remove="() => (phoneDialog.form.imageUrl = '')"
          >
            <img
              v-if="phoneDialog.form.imageUrl"
              :src="phoneDialog.form.imageUrl"
              style="width: 100px; height: 100px"
            />
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>
        <el-form-item label="主页排序" prop="homeSort">
          <el-input-number v-model="phoneDialog.form.homeSort" :min="0" />
        </el-form-item>
        <el-form-item label="页面排序" prop="pageSort">
          <el-input-number v-model="phoneDialog.form.pageSort" :min="0" />
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose" :disabled="phoneDialog.loading"
          >取 消</el-button
        >
        <el-button
          type="primary"
          @click="submitPhoneDialog()"
          :loading="phoneDialog.loading"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import * as api from "@/api/merchant";
export default {
  data() {
    return {
      BASE_URL: process.env.VUE_APP_SERVER_URL,
      expandRowKeys: [],
      rules: {
        baseTypeName: [
          { required: true, message: "请输入类型名称", trigger: "blur" },
        ],
        serviceTypeName: [
          { required: true, message: "请输入商户类型名称", trigger: "blur" },
        ],
        description: [
          { required: true, message: "请输入服务描述", trigger: "blur" },
        ],
        iconUrl: [
          { required: true, message: "请上传类别图片", trigger: "change" },
        ],
        imageUrl: [
          { required: true, message: "请上传主页图片", trigger: "change" },
        ],
        homeSort: [
          { required: true, message: "请输入主页排序", trigger: "blur" },
          { type: "number", message: "必须为数字值", trigger: "blur" },
        ],
        pageSort: [
          { required: true, message: "请输入页面排序", trigger: "blur" },
          { type: "number", message: "必须为数字值", trigger: "blur" },
        ],
      },
      listLoading: false,
      tableData: [],
      typeDialog: {
        isShow: false,
        form: {
          baseTypeName: "",
          type: 111,
        },
        loading: false,
      },
      phoneDialog: {
        isShow: false,
        form: {
          serviceTypeName: "",
          description: "",
          iconUrl: "",
          imageUrl: "",
          homeSort: "",
          pageSort: "",
          baseTypeId: "",
          homeSort: 0,
          pageSort: 0,
        },
        loading: false,
      },
    };
  },
  created() {
    this.getAllConvenient();
  },
  methods: {
    async getAllConvenient() {
      try {
        this.listLoading = true;
        const { data } = await api.getAllBaseTypeList();
        console.log(data);
        this.tableData = data;
      } catch (err) {
        console.error(err);
      } finally {
        this.listLoading = false;
      }
    },
    handleSuccess(response, file, fileList) {
      console.log("handleSuccess==>", response, file, fileList);
      const { code, data, message } = response;
      if (code == 200) {
        this.phoneDialog.form.imageUrl = data;
      } else {
        this.$message.error(message || "上传失败");
      }
    },
    handleSuccess1(response, file, fileList) {
      console.log("handleSuccess==>", response, file, fileList);
      const { code, data, message } = response;
      if (code == 200) {
        this.phoneDialog.form.iconUrl = data;
      } else {
        this.$message.error(message || "上传失败");
      }
    },
    handleUpload(file) {
      console.log("handleUpload==>", file);
      const isJPG = ["image/jpg", "image/png", "image/jpeg"].includes(
        file.type
      );
      const isLt2M = file.size / 1024 / 1024 < 10;
      console.log("isLt2M", isLt2M);
      if (!isJPG) {
        return this.$message.error("上传图片只能是 JPG/PNG/JPEG 格式!");
      }
      if (!isLt2M) {
        alert();
        return this.$message.error("上传图片大小不能超过 10MB!");
      }
    },
    openAddType(row = {}) {
      if (row.baseId) {
        //编辑
        this.expandRowKeys = [row.baseId];
        this.typeDialog = {
          isShow: true,
          form: {
            baseTypeName: row.baseName,
            id: row.baseId,
            type: row.type,
          },
          loading: false,
          isEdit: true,
        };
      } else {
        //新增
        this.typeDialog = {
          isShow: true,
          form: {
            type: 111,
          },
          loading: false,
          isEdit: false,
        };
      }
      this.$nextTick(() => {
        this.$refs.typeForm.clearValidate();
      });
    },
    openAddPhone(typeRow, phoneRow = {}) {
      if (phoneRow.id) {
        //修改
        this.phoneDialog = {
          isShow: true,
          form: {
            serviceTypeName: phoneRow.serviceTypeName,
            baseTypeId: typeRow.baseId,
            ...phoneRow,
          },
          loading: false,
          isEdit: true,
        };
      } else {
        this.expandRowKeys = [typeRow.baseId];
        //新增
        this.phoneDialog = {
          isShow: true,
          form: {
            serviceTypeName: "",
            description: "",
            iconUrl: "",
            imageUrl: "",
            homeSort: 0,
            pageSort: 0,
            baseTypeId: typeRow.baseId,
            homeSort: 0,
            pageSort: 0,
          },
          loading: false,
          isEdit: false,
        };
      }
      this.$nextTick(() => {
        this.$refs.phoneForm.clearValidate();
      });
    },

    handleClose() {
      this.typeDialog = {
        isShow: false,
        form: {
          typeName: "",
        },
        loading: false,
        isEdit: true,
      };
      this.phoneDialog = {
        isShow: false,
        form: {
          convenientTypeId: "",
          telephoneName: "",
          telephoneNumber: "",
        },
        loading: false,
        isEdit: true,
      };
    },
    async submitTypeDialog() {
      await this.$refs.typeForm.validate();
      console.log(this.typeDialog.form);
      try {
        this.typeDialog.loading = true;
        if (this.typeDialog.form?.id) {
          //修改
          await api.modifyBaseType(this.typeDialog.form);
        } else {
          //新增
          await api.addBaseType(this.typeDialog.form);
        }

        this.$message({
          type: "success",
          message: "提交成功",
        });
        this.handleClose();
        this.getAllConvenient();
      } catch (err) {
        console.error(err);
      } finally {
        this.typeDialog.loading = false;
      }
    },
    async submitPhoneDialog() {
      await this.$refs.phoneForm.validate();
      try {
        this.phoneDialog.loading = true;
        if (this.phoneDialog.form?.id) {
          //修改
          await api.modifyServiceType(this.phoneDialog.form);
        } else {
          //新增
          await api.addServiceType(this.phoneDialog.form);
        }
        this.$message({
          type: "success",
          message: "提交成功",
        });
        this.handleClose();
        this.getAllConvenient();
      } catch (err) {
        console.error(err);
      } finally {
        this.phoneDialog.loading = false;
      }
    },
    // 删除服务类型
    async delServiceType(row) {
      try {
        await this.$confirm('确认删除该服务类型吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });
        
        this.listLoading = true;
        await api.removeServiceType(row.id);
        this.$message({
          type: 'success',
          message: '删除成功'
        });
        this.getAllConvenient();
      } catch (err) {
        if (err !== 'cancel') {
          console.error(err);
          this.$message.error('删除失败');
        }
      } finally {
        this.listLoading = false;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.wrap {
  // max-width: 1200px;
  margin: 0 auto;
}

.btnBox {
  text-align: right;
  padding: 10px 0;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}
.avatar {
  width: 100px;
  height: 100px;
  display: block;
}

::v-deep .el-upload {
  width: 100px;
  height: 100px;
  border: 1px dashed #d9d9d9;
}
</style>
