<!--
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-05-27 10:29:24
 * @LastEditors: wangxingyue
 * @LastEditTime: 2025-05-27 14:53:34
 * @FilePath: \ban-ban\运营端\admin\src\layout\components\Sidebar\index.vue
-->
<template>
  <div :class="{ 'has-logo': showLogo }">
    <logo v-if="showLogo" :collapse="isCollapse" />
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :background-color="variables.menuBg"
        :text-color="variables.menuText"
        :unique-opened="false"
        :active-text-color="variables.menuActiveText"
        :collapse-transition="false"
        :default-openeds="defaultOpenedsIndexs"
        mode="vertical"
        router
        @select="handleMenuSelect"
      >
        <sidebar-item
          v-for="route in routes"
          :key="route.path"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import Logo from "./Logo";
import SidebarItem from "./SidebarItem";
import variables from "@/styles/variables.scss";

export default {
  components: { SidebarItem, Logo },
  computed: {
    ...mapGetters(["sidebar", "userInfo"]),
    routes() {
      const allRoutes = this.$router.options.routes;
      // 根据status过滤路由
      // if (this.userInfo.status === 0) {
      //   // 当status=0时，过滤掉小区管理和财务管理模块
      //   return allRoutes.filter(route =>
      //     route.path !== '/manage' && route.path !== '/financial'
      //   );
      // }
      // status=1或其他状态时，显示所有路由
      return allRoutes;
    },
    defaultOpenedsIndexs() {
      return this.routes.map((e, i) => e.path);
    },
    activeMenu() {
      const route = this.$route;
      const { meta, path } = route;
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu;
      }
      return path;
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo;
    },
    variables() {
      return variables;
    },
    isCollapse() {
      return !this.sidebar.opened;
    },
  },
  mounted() {
    // 获取待审核商户数量
    this.fetchWaitShopNum();
    // // 设置定时器，每5分钟更新一次
    // this.timer = setInterval(() => {
    //   this.fetchWaitShopNum();
    // }, 5 * 60 * 1000);
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer);
    }
  },
  methods: {
    async fetchWaitShopNum() {
      try {
        await this.$store.dispatch('merchant/fetchWaitShopNum');
      } catch (error) {
        console.error('获取待审核商户数量失败:', error);
      }
    },

    handleMenuSelect(index, indexPath) {
      console.log('Menu selected:', index, indexPath);

      // 获取当前路由
      const currentPath = this.$route.path;

      // 如果点击的是当前路由，则刷新页面
      if (currentPath === index) {
        this.refreshCurrentPage(index);
      }
    },

    refreshCurrentPage(targetPath) {
      // 使用refresh页面进行刷新
      this.$router.replace({
        path: '/refresh',
        query: {
          redirect: targetPath,
          timestamp: Date.now() // 添加时间戳确保每次都是新的路由
        }
      });
    }
  }
};
</script>
