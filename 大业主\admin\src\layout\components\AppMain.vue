<!--
 * @Author: CBB
 * @Date: 2025-03-16 21:35:03
 * @LastEditTime: 2025-03-18 01:50:07
 * @LastEditors: CBB
 * @Description:
 * @FilePath: \安小页\物业端\vue-admin-template-master\src\layout\components\AppMain.vue
-->
<template>
  <section class="app-main">
    <el-card shadow="always">
      <template #header>
        <div class="title">
          <i @click="goBack()" v-show="isShowTitleBack" class="el-icon-back" />
          <h5>{{ name }}</h5>
        </div>
      </template>
      <!-- <transition name="fade-transform" mode="out-in">
        <router-view :key="key" />
      </transition> -->
      <transition name="fade-transform" mode="out-in">
        <keep-alive>
          <router-view v-if="keepAlive" :key="$route.path" />
        </keep-alive>
      </transition>

      <transition name="fade-transform" mode="out-in">
        <router-view v-if="!keepAlive" :key="$route.path" />
      </transition>
    </el-card>
  </section>
</template>

<script>
export default {
  name: "AppMain",
  computed: {
    key() {
      return this.$route.path;
    },
    name() {
      return this.$route.meta?.title || "";
    },
    isShowTitleBack() {
      return this.$route.meta?.isShowTitleBack || false;
    },
    keepAlive() {
      console.log("keep==>", this.$route.name, this.$route.meta?.keepAlive);

      return this.$route.meta?.keepAlive || false;
    },
  },
  methods: {
    goBack() {
      this.$router.go(-1);
    },
  },
};
</script>

<style scoped lang="scss">
.app-main {
  /*50 = navbar  */
  min-height: calc(100vh - 50px);
  width: 100%;
  position: relative;
  overflow: hidden;
  padding: 20px;
}

.fixed-header + .app-main {
  padding-top: 70px;
}

::v-deep .el-card__header {
  background: #f5f9ff;

  .title {
    display: flex;
    align-items: center;
    h5 {
      font-size: 18px;
      padding-left: 10px;
    }
    i {
      display: block;
      padding: 5px;
      border-radius: 50%;
      font-size: 14px;
      border: 2px solid #303133;
      font-weight: 600;
      cursor: pointer;
    }
  }
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 15px;
  }
}
</style>
