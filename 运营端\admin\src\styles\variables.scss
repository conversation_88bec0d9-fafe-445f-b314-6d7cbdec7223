// sidebar
$menuText: #1a1a1a;
$menuActiveText: #1178fd;
$subMenuActiveText: #1178fd; //https://github.com/ElemeFE/element/issues/12951

$menuActiveBackground: #f0f5ff;

$menuBg: #fff;
$menuHover: #f0f5ff;

$subMenuBg: #fff;
$subMenuHover: #f0f5ff;

$sideBarWidth: 210px;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
}
