const ora = require("ora")();
const inquirer = require("inquirer");
const rimraf = require("rimraf");
const { existsSync } = require("fs");
const { resolve } = require("path");
const { green, red, yellow, grey } = require("chalk");
const Client = require("ssh2-sftp-client");

require("dotenv").config({ path: resolve(__dirname, "../.env.production") });

const config = {
  // sftp信息
  host: process.env.MFC_DEPLOY_SFTP_HOST,
  port: process.env.MFC_DEPLOY_SFTP_PROT,
  username: process.env.MFC_DEPLOY_SFTP_USERNAME,
  password: process.env.MFC_DEPLOY_SFTP_PASSWORD,

  // 路径信息
  localPath: resolve(__dirname, "../dist"),
  remotePath: process.env.MFC_DEPLOY_SFTP_REMMOTE_PATH, // 千万别填错 很危险
};

/**
 * 验证路径错误 - 检查本地的刚打包出来的文件夹
 */
if (!existsSync(config.localPath)) {
  throw new Error(yellow("未正确找到打包后的文件夹 请先进行打包..."));
}

/**
 * 验证路径错误 - 检查远程测试服务器上 "源路径" 错误
 */
config.remotePath = config.remotePath.trim(); // 去除空格 并且防止为空
if (!config.remotePath) {
  // 为空的抛错出去
  throw new Error(red("远程服务器 源路径 填写错误 很容易造成服务器错误哦"));
}

/***
 * 确认是否上传...
 */
const prompList = [
  {
    type: "confirm",
    message: "发布到测试环境？",
    name: "isRelease",
    default: true,
  },
  {
    type: "confirm",
    message: "发布成功后是否删除本地dist文件夹",
    name: "isDeleteDist",
    default: true,
    when: (answers) => answers.isRelease,
  },
];

/**
 * 上传逻辑
 */
(async () => {
  const { isRelease, isDeleteDist } = await inquirer.prompt(prompList);

  // 用户选择不发布 直接退出
  if (!isRelease) return false;

  const sftp = new Client(); // 创建客户端

  try {
    // ssh链接
    ora.start("连接测试服务...");
    await sftp.connect(config);
    ora.stop();

    // 打印链接成功信息
    console.log(green("\n连接测试服务成功\n"));

    // 是否含有路径
    const [isHasRemotePath] = await Promise.all([
      sftp.exists(config.remotePath),
    ]);

    // 路径都存在的情况下
    if (isHasRemotePath) {
      // 先删除该文件目录
      await Promise.all([sftp.rmdir(config.remotePath, true)]);
    } else {
      throw new Error("请确认服务器不存在该目录");
    }

    // 然后再创建该远程文件目录
    await Promise.all([sftp.mkdir(config.remotePath, true)]);

    ora.start("正在上传中...");
    await Promise.all([sftp.uploadDir(config.localPath, config.remotePath)]);
    ora.stop();

    console.log(green(`Success! ${grey(`成功发布到测试服务器!`)}`));

    // 用户选择删除本地dist文件
    if (isDeleteDist) rimraf.sync(config.localPath);
  } catch (e) {
    ora.stop();
    console.log(e);
    console.log(red(`发布失败 ${grey("--->")} ${e}`));
  } finally {
    ora.stop();

    sftp.end();
  }
})();
