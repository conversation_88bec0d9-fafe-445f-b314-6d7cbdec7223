/*
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-05-27 15:00:59
 * @LastEditors: wangxingyue
 * @LastEditTime: 2025-07-10 10:34:03
 * @FilePath: \ban-ban\运营端\admin\src\api\landlord.js
 */
import request from "@/utils/request";

// 房东运营数据
export function getLandlordReport(data) {
  return request({
    url: "/admin/report/getLandlordReport",
    method: "post",
    requestType: "json",
    data,
  });
}

// 房东详情
export function getOwnerInfo(id) {
  return request({
    url: "/admin/wy/info/getOwnerInfo",
    method: "get",
    params: {
      id
    }
  });
}

// 提现记录
export function getWithdrawalList(data) {
  return request({
    url: "/admin/wy/info/getWithdrawalList",
    method: "post",
    requestType: "json",
    data,
  });
}

// 提现记录
export function getPayHistoryList(data) {
  return request({
    url: "/admin/wy/info/getPayHistoryList",
    method: "post",
    requestType: "json",
    data,
  });
}