<!--
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-05-20 15:59:55
 * @LastEditors: wangxingyue
 * @LastEditTime: 2025-05-27 14:53:54
 * @FilePath: \ban-ban\大业主\admin\src\layout\components\Sidebar\index.vue
-->
<template>
  <div :class="{ 'has-logo': showLogo }">
    <logo v-if="showLogo" :collapse="isCollapse" />
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :background-color="variables.menuBg"
        :text-color="variables.menuText"
        :unique-opened="false"
        :active-text-color="variables.menuActiveText"
        :collapse-transition="false"
        :default-openeds="defaultOpenedsIndexs"
        mode="vertical"
        router
        @select="handleMenuSelect"
      >
        <sidebar-item
          v-for="route in routes"
          :key="route.path"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import Logo from "./Logo";
import SidebarItem from "./SidebarItem";
import variables from "@/styles/variables.scss";

export default {
  components: { SidebarItem, Logo },
  computed: {
    ...mapGetters(["sidebar", "userInfo"]),
    routes() {
      const allRoutes = this.$router.options.routes;
      // // 根据status过滤路由
      // if (this.userInfo.status === 0) {
      //   // 当status=0时，过滤掉小区管理和财务管理模块
      //   return allRoutes.filter(route =>
      //     route.path !== '/manage' && route.path !== '/financial'
      //   );
      // }
      // status=1或其他状态时，显示所有路由
      return allRoutes;
    },
    defaultOpenedsIndexs() {
      return this.routes.map((e) => e.path);
    },
    activeMenu() {
      const route = this.$route;
      const { meta, path } = route;
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu;
      }
      return path;
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo;
    },
    variables() {
      return variables;
    },
    isCollapse() {
      return !this.sidebar.opened;
    },
  },
  methods: {
    handleMenuSelect(index, indexPath) {
      console.log('Menu selected:', index, indexPath);

      // 获取当前路由
      const currentPath = this.$route.path;

      // 如果点击的是当前路由，则刷新页面
      if (currentPath === index) {
        this.refreshCurrentPage(index);
      }
    },

    refreshCurrentPage(targetPath) {
      // 方法1：使用refresh页面进行刷新（推荐）
      this.$router.replace({
        path: '/refresh',
        query: {
          redirect: targetPath,
          timestamp: Date.now() // 添加时间戳确保每次都是新的路由
        }
      });

      // 方法2：如果你想要强制刷新整个页面，可以使用下面的代码
      // window.location.reload();

      // 方法3：如果你想要清除keep-alive缓存，可以使用下面的代码
      // this.$nextTick(() => {
      //   const cachedViews = this.$store.state.tagsView?.cachedViews || [];
      //   if (cachedViews.length > 0) {
      //     this.$store.dispatch('tagsView/delCachedView', this.$route);
      //   }
      // });
    }
  }
};
</script>
