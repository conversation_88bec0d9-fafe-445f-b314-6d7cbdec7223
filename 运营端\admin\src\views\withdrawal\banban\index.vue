<!--
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-05-27 10:46:12
 * @LastEditors: wangxingyue
 * @LastEditTime: 2025-07-25 14:54:41
 * @FilePath: \ban-ban\运营端\admin\src\views\withdrawal\banban\index.vue
-->
<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form inline>
        <el-form-item label="状态：">
          <el-select v-model="querySearch.status" placeholder="请选择状态">
            <el-option
              v-for="(item, index) in statusOptions"
              :key="index + item.value"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        &nbsp;
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="fetchData()"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" @click="resetSearch()"
            >重置</el-button
          >
          <el-button
            :disabled="!multipleSelection.length"
            type="success"
            @click="handleApply(multipleSelection, 1)"
            >批量通过</el-button
          >
          <el-button
            :disabled="!multipleSelection.length"
            type="warning"
            @click="handleApply(multipleSelection, 0)"
            >批量拒绝</el-button
          >
        </el-form-item>
      </el-form>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      max-height="580"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
      ref="multipleTable"
      @selection-change="handleSelectionChange"
      :row-key="(_) => _.id"
    >
      <el-table-column
        type="selection"
        width="55"
        align="center"
        reserve-selection
      />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="昵称" align="center" prop="nickName" />
      <el-table-column label="提现金额" align="center" prop="money" />
      <el-table-column label="申请日期" align="center" prop="applyTime">
        <template #default="{ row }">{{
          parseTime(row.applyTime, "YYYY-MM-DD")
        }}</template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <!-- 状态：0 申请  1 拒绝  2 成功 -->
        <template #default="{ row }">
          <el-tag :type="statusFilter(row.status).type">{{
            statusFilter(row.status).name
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="扣除积分" align="center" prop="points" />
      <el-table-column label="订单号" align="center" prop="orderNo" />
      <el-table-column label="操作" align="center">
        <template #default="{ row }">
          <template v-if="row.status == 0">
            <el-button type="text" @click="handleApply([row.id], 1)"
              >通过</el-button
            >
            <el-button type="text" @click="handleApply([row.id], 0)"
              >拒绝</el-button
            >
          </template>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination">
      <el-pagination
        @size-change="fetchData"
        @current-change="fetchData"
        :current-page.sync="querySearch.pageNo"
        :page-sizes="[15, 30, 50, 100]"
        :page-size.sync="querySearch.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="querySearch.total"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import * as api from "@/api/withdrawal";
export default {
  name: "Withdrawallist",
  data() {
    return {
      list: [],
      listLoading: true,
      querySearch: {
        pageNo: 1,
        pageSize: 50,
        total: 0,
        status: "",
      },

      statusOptions: [
        {
          name: "全部",
          value: null,
          type: "success",
        },
        {
          name: "待审核",
          value: 0,
          type: "",
        },
        {
          name: "审核通过",
          value: 1,
          type: "success",
        },
        {
          name: "审核失败",
          value: 2,
          type: "warning",
        },
        {
          name: "打款中",
          value: 3,
          type: "success",
        },
        {
          name: "提现成功",
          value: 4,
          type: "success",
        },
        {
          name: "打款失败",
          value: 5,
          type: "danger",
        },
      ],

      multipleSelection: [],
    };
  },
  created() {
    this.fetchData();
  },
  methods: {
    statusFilter(status) {
      return (
        this.statusOptions.find((e) => e.value == status) || {
          name: "未知",
          value: "",
          type: "",
        }
      );
    },
    async fetchData() {
      try {
        this.listLoading = true;
        const { data: res } = await api.getWithdrawalOrders(this.querySearch);
        const { data, pageNo, pageSize, totalElements } = res;
        this.list = data;
        this.querySearch = {
          pageNo,
          pageSize,
          total: totalElements,
          status: this.querySearch.status,
        };
      } catch (err) {
        console.error(err);
      } finally {
        this.listLoading = false;
      }
    },

    // 重置搜索条件
    resetSearch() {
      this.querySearch = {
        pageNo: 1,
        pageSize: 50,
        total: 0,
        status: "",
      };
      // 重置到第一页并获取数据
      this.querySearch.pageNo = 1;
      this.multipleSelection = [];
      this.$refs.multipleTable.clearSelection();
      this.fetchData();
    },

    handleSelectionChange(val) {
      console.log(val);
      this.multipleSelection = val.map((e) => e.id);
    },

    async handleApply(ids, type) {
      await this.$confirm(`确定${type === 1 ? "通过" : "拒绝"}吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      });
      try {
        if (type == 1) {
          //通过
          await api.passOrders({ ids: ids.join(",") });
        } else {
          await api.rejectOrders({ ids: ids.join(",") });
        }
        this.$message({
          type: "success",
          message: "操作成功",
        });
        this.multipleSelection = [];
        this.$refs.multipleTable.clearSelection();
        this.fetchData();
      } catch (err) {
        console.error(err);
      }
    },
  },
};
</script>

<style scoped lang="scss">
.filter-container {
  padding-bottom: 10px;
  margin-bottom: 15px;
}

.pagination {
  padding-top: 20px;
  text-align: right;
}
</style>