import Vue from "vue";
import Router from "vue-router";

Vue.use(Router);

/* Layout */
import Layout from "@/layout";
import Secondary from "@/layout/secondary";
import Login from "@/views/login/index";
import Refresh from "@/views/refresh.vue";

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'/'el-icon-x' the icon show in the sidebar
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    path: "/login",
    component: Login,
    hidden: true,
  },

  {
    path: "/404",
    component: () => import("@/views/404"),
    hidden: true,
  },
  {
    path: "/",
    redirect: "/data",
    hidden: true,
  },
  {
    path: "/data",
    component: Layout,
    redirect: "/data/list",
    meta: { title: "平台数据" },
    alwaysShow: true,
    children: [
      {
        path: "list",
        name: "Datalist",
        component: () => import("@/views/data/list/index"),
        meta: { title: "平台数据", requiredEdit: true },
      }
    ],
  },
  // {
  //   path: "/user",
  //   component: Layout,
  //   redirect: "/user/list",
  //   meta: { title: "用户管理" },
  //   alwaysShow: true,
  //   children: [
  //     {
  //       path: "list",
  //       name: "Userlist",
  //       component: () => import("@/views/user/list/index"),
  //       meta: { title: "用户管理", requiredEdit: true },
  //     }
  //   ],
  // },
  {
    path: "/landlord",
    component: Layout,
    redirect: "/landlord/data",
    meta: { title: "房东管理" },
    alwaysShow: true,
    children: [
      {
        path: "data",
        name: "Landlorddata",
        component: () => import("@/views/landlord/data/index"),
        meta: { title: "运营数据", requiredEdit: true },
      },
      {
        path: "list",
        name: "Landlordlist",
        component: () => import("@/views/landlord/list/index"),
        meta: { title: "房东列表", requiredEdit: true },
        children: [
          {
            path: "infoList/:openKey",
            name: "LandlordInfoList",
            component: () => import("@/views/landlord/list/infolist/index"),
            meta: {
              title: "房东详情",
              activeMenu: "/landlord/list",
              isShowTitleBack: true,
              keepAlive: true,
            },
            hidden: true,
            children: [
              {
                path: "collectionRecord/:openKey",
                name: "LandlordcollectionRecord",
                component: () => import("@/views/landlord/list/collectionRecord/index"),
                meta: {
                  title: "收款记录",
                  activeMenu: "/landlord/list",
                  isShowTitleBack: true,
                },
                hidden: true,
              },
              {
                path: "bill/:openKey",
                name: "LandlordwithdrawalRecord",
                component: () => import("@/views/landlord/list/withdrawalRecord/index"),
                meta: {
                  title: "提现记录",
                  activeMenu: "/landlord/list",
                  isShowTitleBack: true,
                },
                hidden: true,
              }
            ],
          },
        ],
      }
      // {
      //   path: "list",
      //   name: "Landlordlist",
      //   component: () => import("@/views/landlord/list/index"),
      //   meta: { title: "房东列表", requiredEdit: true },
      //   children: [
      //     {
      //       path: "collectionRecord/:openKey",
      //       name: "LandlordcollectionRecord",
      //       component: () => import("@/views/landlord/list/collectionRecord/index"),
      //       meta: {
      //         title: "收款记录",
      //         activeMenu: "/landlord/data",
      //         isShowTitleBack: true,
      //         keepAlive: true,
      //       },
      //       hidden: true,
      //     },
      //     {
      //       path: "bill/:openKey",
      //       name: "LandlordwithdrawalRecord",
      //       component: () => import("@/views/landlord/list/withdrawalRecord/index"),
      //       meta: {
      //         title: "提现记录",
      //         activeMenu: "/landlord/data",
      //         isShowTitleBack: true,
      //         keepAlive: true
      //       },
      //       hidden: true,
      //     }
      //   ],
      // }
    ],
  },
  {
    path: "/merchant",
    component: Layout,
    redirect: "/merchant/data",
    meta: { title: "商户管理" },
    alwaysShow: true,
    children: [
      {
        path: "data",
        name: "Merchantdata",
        component: () => import("@/views/merchant/data/index"),
        meta: { title: "运营数据", requiredEdit: true },
      },
      {
        path: "list",
        name: "Merchantlist",
        component: () => import("@/views/merchant/list/index"),
        meta: { title: "商户列表", requiredEdit: true },
      },
      {
        path: "type",
        name: "Merchanttype",
        component: () => import("@/views/merchant/type/index"),
        meta: { title: "商户类型", requiredEdit: true },
      }
    ],
  },
  {
    path: "/onlineretailers",
    component: Layout,
    redirect: "/onlineretailers/data",
    meta: { title: "电商管理" },
    alwaysShow: true,
    children: [
      {
        path: "data",
        name: "Onlineretailersdata",
        component: () => import("@/views/onlineretailers/data/index"),
        meta: { title: "运营数据", requiredEdit: true },
      },
      {
        path: "list",
        name: "Onlineretailerslist",
        component: () => import("@/views/onlineretailers/list/index"),
        meta: { title: "订单列表", requiredEdit: true },
      }
    ],
  },
  {
    path: "/withdrawal",
    component: Layout,
    redirect: "/withdrawal/data",
    meta: { title: "提现管理" },
    alwaysShow: true,
    children: [
      {
        path: "list",
        name: "Withdrawallist",
        component: () => import("@/views/withdrawal/list/index"),
        meta: { title: "提现管理", requiredEdit: true },
      },
      {
        path: "banban",
        name: "WithdrawalBanban",
        component: () => import("@/views/withdrawal/banban/index"),
        meta: { title: "小程序提现管理", requiredEdit: true },
      }
    ],
  },
  {
    path: "/systemSet",
    component: Layout,
    redirect: "/systemSet/system",
    meta: { title: "系统设置" },
    alwaysShow: true,
    children: [
      {
        path: "system",
        name: "systemSet",
        component: () => import("@/views/systemSet/system/index"),
        meta: { title: "系统设置", requiredEdit: true },
      },
      {
        path: "banbanset",
        name: "systemSet",
        component: () => import("@/views/systemSet/banbanset/index"),
        meta: { title: "平台设置", requiredEdit: true },
      },
      {
        path: "notice",
        name: "systemSet",
        component: () => import("@/views/systemSet/notice/index"),
        meta: { title: "通知公告", requiredEdit: true },
      }
    ],
  },
  {
    path: "/refresh",
    name: "Refresh",
    component: Refresh,
    hidden: true,
  },

  // 404 page must be placed at the end !!!
  { path: "*", redirect: "/404", hidden: true },
];

const createRouter = () =>
  new Router({
    // mode: 'history', // require service support
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRoutes,
  });

const router = createRouter();

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter();
  router.matcher = newRouter.matcher; // reset router
}

export default router;
