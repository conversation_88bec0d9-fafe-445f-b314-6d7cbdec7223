/*
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-05-27 10:29:24
 * @LastEditors: wangxingyue
 * @LastEditTime: 2025-06-27 14:32:32
 * @FilePath: \ban-ban\运营端\admin\src\api\user.js
 */
import request from "@/utils/request";

export function getSmsCode(telephone) {
  return request({
    url: "/auth/getPhone",
    method: "post",
    data: {
      telephone,
    },
  });
}

/**
 *
 * telephone=13516440569 必填
code = 123456 必填
checked = 1   隐私协议是否选中  必填 0 未选中 1选中

@returns  
 "token": "Bearer 1901175636886687744", //token
 "status": 3 //账户状态
 */
export function login(data) {
  return request({
    url: "/admin/auth/login",
    method: "post",
    // requestType: "json",
    data,
  });
}

// 退出登录
export function logout() {
  return request({
    url: "/admin/auth/logout",
    method: "get",
  });
}

// 运营端用户密码修改
export function modifyPassword(data) {
  return request({
    url: "/admin/auth/modifyPassword",
    method: "post",
    requestType: "json",
    data,
  });
}

//获取小区id与状态
export function getAccountInfo() {
  return request({
    url: "/auth/getAccountInfo",
    method: "get",
  });
}




// 分页获取业主用户信息
export function findAccountByPage(data) {
  return request({
    url: "/admin/account/findAccountByPage",
    method: "post",
    requestType: "json",
    data,
  });
}

// 添加业主账号
export function addAccount(data) {
  return request({
    url: "/admin/account/addAccount",
    method: "post",
    requestType: "json",
    data,
  });
}

// 根据id获取业主账户信息
export function getAccountById(id) {
  return request({
    url: "/admin/account/getInfoById",
    method: "get",
    params: {
      id
    }
  });
}

// 修改用户信息
export function modifyAccount(data) {
  return request({
    url: "/admin/account/modifyAccount",
    method: "post",
    requestType: "json",
    data,
  });
}

// 启用或禁用业主账号
export function enableOrDisableAccount(id) {
  return request({
    url: "/admin/account/enableOrDisableAccount",
    method: "get",
    params: {
      id
    }
  });
}

// 删除业主账号
export function deleteAccount(id) {
  return request({
    url: "/admin/account/deleteAccount",
    method: "get",
    params: {
      id
    }
  });
}
