<!--
 * @Author: CBB
 * @Date: 2025-03-18 19:00:23
 * @LastEditTime: 2025-07-15 18:10:51
 * @LastEditors: wangxingyue
 * @Description:
 * @FilePath: \ban-ban\大业主\admin\src\layout\components\Sidebar\Item.vue
-->
<script>
import stepEnum from "@/plugins/stepEnum";
import { mapState } from "vuex";

export default {
  name: "MenuItem",
  functional: false,
  props: {
    icon: {
      type: String,
      default: "",
    },
    title: {
      type: String,
      default: "",
    },
    requiredEdit: {
      type: Boolean,
      default: false,
    },
    step: {
      type: Number,
      default: null,
    },
    name: {
      type: String,
      default: "",
    },
  },
  computed: {
    ...mapState({
      status: state => state.user.userInfo.status
    })
  },
  render(h) {
    const { icon, title, requiredEdit, name, step } = this;
    let nowPage = {};
    for (const i in stepEnum) {
      if (Object.prototype.hasOwnProperty.call(stepEnum, i)) {
        const e = stepEnum[i];
        if (e.name === name) {
          nowPage = e;
        }
      }
    }
    const vnodes = [];
    // if (nowPage?.title && step < 8 && nowPage?.step >= step && requiredEdit && this.status === 0) {
    if (nowPage?.title && requiredEdit && this.status !== 1) {
      vnodes.push(
        <el-tag class="item-tag" size="mini" effect="plain" type="warning">
          {"必填"}
        </el-tag>
      );
    }

    if (icon) {
      if (icon.includes("el-icon")) {
        vnodes.push(<i class={[icon, "sub-el-icon"]} />);
      } else if (icon.includes("png")) {
        vnodes.push(<img class={["sub-el-img"]} src={icon} />);
      } else {
        vnodes.push(<svg-icon icon-class={icon} />);
      }
    }

    if (title) {
      vnodes.push(<span slot="title">{title}</span>);
    }
    return <div class="menu-item-wrapper">{vnodes}</div>;
  },
};
</script>

<style scoped>
.sub-el-icon {
  color: currentColor;
  width: 1em;
  height: 1em;
}

.sub-el-img {
  width: 25px;
  height: 25px;
  object-fit: contain;
  display: inline-block;
  margin-right: 10px;
}

.item-tag {
  transform: scale(0.9);
  margin-right: 2px;
  vertical-align: middle;
}

.menu-item-wrapper {
  display: inline-flex;
  align-items: center;
}
</style>
