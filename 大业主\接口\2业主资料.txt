1、获取当前业主资料
/sw/wy/ownerInfo/getOwnerInfo
GET
无参数
响应:
{
	code:200,
	message:null,
	data:{
  "openKey": "唯一标识",
  "companyName": "公司名称",
  "phone": "联系电话",
  "address": "联系地址",
  "businessLicenseUrl": "营业执照图片URL",
  "responsiblePerson": "负责人姓名",
  "contractUrl": "合同图片URL",
  "remark": "备注信息"
}
}

2、设置业主资料
/sw/wy/ownerInfo/setOwnerInfo
post:
content-type:application/json
参数:
{
  "openKey": "123e4567-e89b-12d3-a456-426614174000",
  "companyName": "示例科技有限公司",
  "phone": "***********",
  "address": "北京市海淀区科技园路1号",
  "businessLicenseUrl": "https://example.com/licenses/12345.jpg",
  "responsiblePerson": "张三",
  "contractUrl": "https://example.com/contracts/2023-001.pdf"
}
响应:
{
	code:200,
	message:null,
	data:null
}
